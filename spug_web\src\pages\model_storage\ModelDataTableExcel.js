import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Card, Button, message, Space, Modal, Input, Form, Tooltip, Popover, Dropdown } from 'antd';
import { 
  SaveOutlined, PlusOutlined, DeleteOutlined, ReloadOutlined, 
  CopyOutlined, FileExcelOutlined, UndoOutlined, RedoOutlined,
  MoreOutlined, FilterOutlined, ExportOutlined, ImportOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import http from 'libs/http';
import './ModelDataTableExcel.css';

// 表格列配置
const COLUMNS_CONFIG = [
  { key: 'id', title: 'ID', width: 60, type: 'number', fixed: 'left', editable: false },
  { key: 'filename', title: '文件名', width: 150, type: 'text', fixed: 'left', required: true },
  { key: 'success_requests', title: '成功请求数', width: 120, type: 'number' },
  { key: 'benchmark_duration', title: '基准时长(s)', width: 120, type: 'number', step: 0.01 },
  { key: 'input_tokens', title: '输入Token总数', width: 130, type: 'number' },
  { key: 'output_tokens', title: '生成Token总数', width: 130, type: 'number' },
  { key: 'request_throughput', title: '请求吞吐量(req/s)', width: 150, type: 'number', step: 0.01 },
  { key: 'output_token_throughput', title: '输出Token吞吐量(tok/s)', width: 180, type: 'number', step: 0.01 },
  { key: 'total_token_throughput', title: '总Token吞吐量(tok/s)', width: 170, type: 'number', step: 0.01 },
  { key: 'avg_ttft', title: '平均TTFT(ms)', width: 130, type: 'number', step: 0.01 },
  { key: 'median_ttft', title: '中位TTFT(ms)', width: 130, type: 'number', step: 0.01 },
  { key: 'p99_ttft', title: 'P99 TTFT(ms)', width: 130, type: 'number', step: 0.01 },
  { key: 'avg_tpot', title: '平均TPOT(ms)', width: 130, type: 'number', step: 0.01 },
  { key: 'median_tpot', title: '中位TPOT(ms)', width: 130, type: 'number', step: 0.01 },
  { key: 'p99_tpot', title: 'P99 TPOT(ms)', width: 130, type: 'number', step: 0.01 },
];

function ModelDataTableExcel() {
  const { modelName } = useParams();

  // 调试信息


  // 状态管理
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [selectedCells, setSelectedCells] = useState(new Set());
  const [selectedRows, setSelectedRows] = useState(new Set());
  const [activeCell, setActiveCell] = useState(null); // 当前激活的单元格
  const [editingCell, setEditingCell] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [filters, setFilters] = useState({});
  const [copyBuffer, setCopyBuffer] = useState(null);
  const [contextMenu, setContextMenu] = useState({ visible: false, x: 0, y: 0, cellId: null });
  const [isDragging, setIsDragging] = useState(false);
  const [dragSelection, setDragSelection] = useState({ start: null, end: null });
  
  // 引用
  const tableRef = useRef(null);
  const containerRef = useRef(null);
  const editInputRef = useRef(null);
  const autosaveTimeoutRef = useRef(null);
  
  // 生成数字ID
  const generateId = () => {
    // 获取当前数据中最大的数字ID，然后+1
    const maxId = Math.max(
      ...data
        .map(row => parseInt(row.id))
        .filter(id => !isNaN(id)),
      0
    );
    return maxId + 1;
  };

  // 创建新行数据
  const createNewRow = (id = null) => {
    const row = { id: id || generateId(), _isNew: !id, _modified: false };
    COLUMNS_CONFIG.forEach(col => {
      if (col.key !== 'id') {
        row[col.key] = col.type === 'number' ? 0 : '';
      }
    });
    return row;
  };

  // 历史记录管理
  const addToHistory = useCallback((newData) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(JSON.parse(JSON.stringify(newData)));
    if (newHistory.length > 50) newHistory.shift(); // 限制历史记录数量
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [history, historyIndex]);

  // 自动保存功能
  const scheduleAutosave = useCallback(() => {
    if (autosaveTimeoutRef.current) {
      clearTimeout(autosaveTimeoutRef.current);
    }
    
    autosaveTimeoutRef.current = setTimeout(() => {
      const modifiedRows = data.filter(row => row._modified || row._isNew);
      if (modifiedRows.length > 0) {
        message.info('正在自动保存...', 1);
        saveData(true); // 静默保存
      }
    }, 30000); // 30秒后自动保存
  }, [data]);

  // 撤销操作
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const prevData = history[historyIndex - 1];
      setData(prevData);
      setHistoryIndex(historyIndex - 1);
      message.info('已撤销操作');
    }
  }, [history, historyIndex]);

  // 重做操作
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const nextData = history[historyIndex + 1];
      setData(nextData);
      setHistoryIndex(historyIndex + 1);
      message.info('已重做操作');
    }
  }, [history, historyIndex]);

  // 解析单元格ID
  const parseCellId = useCallback((cellId) => {
    if (!cellId) return null;
    const [rowId, columnKey] = cellId.split('-');
    const rowIndex = filteredData.findIndex(row => row.id === rowId);
    const columnIndex = COLUMNS_CONFIG.findIndex(col => col.key === columnKey);
    return { rowId, columnKey, rowIndex, columnIndex };
  }, [filteredData]);

  // 键盘导航
  const navigateCell = useCallback((direction, currentCellId) => {
    const current = parseCellId(currentCellId);
    if (!current) return;

    let newRowIndex = current.rowIndex;
    let newColumnIndex = current.columnIndex;

    switch (direction) {
      case 'up':
        newRowIndex = Math.max(0, current.rowIndex - 1);
        break;
      case 'down':
        newRowIndex = Math.min(filteredData.length - 1, current.rowIndex + 1);
        break;
      case 'left':
        newColumnIndex = Math.max(0, current.columnIndex - 1);
        break;
      case 'right':
        newColumnIndex = Math.min(COLUMNS_CONFIG.length - 1, current.columnIndex + 1);
        break;
    }

    const newRowId = filteredData[newRowIndex] && filteredData[newRowIndex].id;
    const newColumnKey = COLUMNS_CONFIG[newColumnIndex] && COLUMNS_CONFIG[newColumnIndex].key;
    
    if (newRowId && newColumnKey) {
      const newCellId = `${newRowId}-${newColumnKey}`;
      setActiveCell(newCellId);
      setSelectedCells(new Set([newCellId]));
      
      // 滚动到可见区域
      scrollToCell(newCellId);
    }
  }, [filteredData, parseCellId]);

  // 滚动到指定单元格
  const scrollToCell = useCallback((cellId) => {
    const cellElement = document.querySelector(`[data-cell-id="${cellId}"]`);
    if (cellElement && containerRef.current) {
      cellElement.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'nearest', 
        inline: 'nearest' 
      });
    }
  }, []);

  // 更新单元格数据
  const updateCell = useCallback((rowId, columnKey, value) => {
    const newData = data.map(row => {
      if (row.id === rowId) {
        const column = COLUMNS_CONFIG.find(col => col.key === columnKey);
        let processedValue = value;
        
        // 数据类型转换
        if (column && column.type === 'number') {
          processedValue = value === '' ? 0 : parseFloat(value) || 0;
        }
        
        return { 
          ...row, 
          [columnKey]: processedValue,
          _modified: true 
        };
      }
      return row;
    });
    
    addToHistory(newData);
    setData(newData);
    setHasUnsavedChanges(true);
    scheduleAutosave();
  }, [data, addToHistory, scheduleAutosave]);

  // 批量更新选中单元格
  const updateSelectedCells = useCallback((value) => {
    if (selectedCells.size === 0) return;
    
    const newData = [...data];
    let hasChanges = false;
    
    selectedCells.forEach(cellId => {
      const [rowId, columnKey] = cellId.split('-');
      const rowIndex = newData.findIndex(row => row.id === rowId);
      if (rowIndex !== -1) {
        const column = COLUMNS_CONFIG.find(col => col.key === columnKey);
        if (column && column.editable !== false) {
          let processedValue = value;
          if (column && column.type === 'number') {
            processedValue = value === '' ? 0 : parseFloat(value) || 0;
          }
          newData[rowIndex] = {
            ...newData[rowIndex],
            [columnKey]: processedValue,
            _modified: true
          };
          hasChanges = true;
        }
      }
    });
    
    if (hasChanges) {
      addToHistory(newData);
      setData(newData);
      setHasUnsavedChanges(true);
      scheduleAutosave();
      message.success(`已更新 ${selectedCells.size} 个单元格`);
    }
  }, [data, selectedCells, addToHistory, scheduleAutosave]);

  // 拖拽填充功能
  const handleDragFill = useCallback((startCellId, endCellId) => {
    const startCell = parseCellId(startCellId);
    const endCell = parseCellId(endCellId);
    
    if (!startCell || !endCell) return;
    
    const startRow = filteredData[startCell.rowIndex];
    const startValue = startRow[startCell.columnKey];
    
    const newData = [...data];
    let fillCount = 0;
    
    // 确定填充范围
    const minRow = Math.min(startCell.rowIndex, endCell.rowIndex);
    const maxRow = Math.max(startCell.rowIndex, endCell.rowIndex);
    const minCol = Math.min(startCell.columnIndex, endCell.columnIndex);
    const maxCol = Math.max(startCell.columnIndex, endCell.columnIndex);
    
    for (let rowIdx = minRow; rowIdx <= maxRow; rowIdx++) {
      for (let colIdx = minCol; colIdx <= maxCol; colIdx++) {
        const targetRow = filteredData[rowIdx];
        const targetColumn = COLUMNS_CONFIG[colIdx];
        
        if (targetRow && targetColumn && targetColumn.editable !== false) {
          const dataRowIndex = newData.findIndex(row => row.id === targetRow.id);
          if (dataRowIndex !== -1) {
            let fillValue = startValue;
            
            // 智能填充：检测数字序列
            if (targetColumn.type === 'number' && typeof startValue === 'number') {
              const rowDiff = rowIdx - startCell.rowIndex;
              const colDiff = colIdx - startCell.columnIndex;
              if (rowDiff !== 0 || colDiff !== 0) {
                fillValue = startValue + rowDiff + colDiff; // 简单的增量填充
              }
            }
            
            newData[dataRowIndex] = {
              ...newData[dataRowIndex],
              [targetColumn.key]: fillValue,
              _modified: true
            };
            fillCount++;
          }
        }
      }
    }
    
    if (fillCount > 0) {
      addToHistory(newData);
      setData(newData);
      setHasUnsavedChanges(true);
      scheduleAutosave();
      message.success(`已填充 ${fillCount} 个单元格`);
    }
  }, [filteredData, data, parseCellId, addToHistory, scheduleAutosave]);

  // 添加空行
  const addEmptyRow = useCallback(() => {
    const newRow = createNewRow();
    const newData = [...data, newRow];
    addToHistory(newData);
    setData(newData);
    setHasUnsavedChanges(true);
    scheduleAutosave();
    message.success('已添加新行');
  }, [data, addToHistory, scheduleAutosave]);

  // 批量添加多行
  const addMultipleRows = useCallback((count = 5) => {
    const newRows = Array.from({ length: count }, () => createNewRow());
    const newData = [...data, ...newRows];
    addToHistory(newData);
    setData(newData);
    setHasUnsavedChanges(true);
    scheduleAutosave();
    message.success(`已添加 ${count} 行`);
  }, [data, addToHistory, scheduleAutosave]);

  // 删除选中行
  const deleteSelectedRows = useCallback(() => {
    if (selectedRows.size === 0) {
      message.warning('请先选择要删除的行');
      return;
    }
    
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRows.size} 行数据吗？`,
      onOk: () => {
        const newData = data.filter(row => !selectedRows.has(row.id));
        addToHistory(newData);
        setData(newData);
        setSelectedRows(new Set());
        setSelectedCells(new Set());
        setActiveCell(null);
        setHasUnsavedChanges(true);
        scheduleAutosave();
        message.success(`已删除 ${selectedRows.size} 行数据`);
      }
    });
  }, [data, selectedRows, addToHistory, scheduleAutosave]);

  // 复制选中单元格
  const copySelectedCells = useCallback(() => {
    if (selectedCells.size === 0) {
      message.warning('请先选择要复制的单元格');
      return;
    }
    
    const cellData = [];
    selectedCells.forEach(cellId => {
      const [rowId, columnKey] = cellId.split('-');
      const row = data.find(r => r.id === rowId);
      if (row) {
        cellData.push({
          rowId,
          columnKey,
          value: row[columnKey]
        });
      }
    });
    
    setCopyBuffer(cellData);
    
    // 将数据复制到系统剪贴板
    const textData = cellData.map(cell => cell.value).join('\t');
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(textData).catch(() => {
        // 静默失败，使用内部剪贴板
      });
    }
    
    message.success(`已复制 ${cellData.length} 个单元格`);
  }, [data, selectedCells]);

  // 粘贴单元格
  const pasteCells = useCallback(() => {
    if (!copyBuffer || copyBuffer.length === 0) {
      // 尝试从系统剪贴板读取
      if (navigator.clipboard && navigator.clipboard.readText) {
        navigator.clipboard.readText().then(text => {
          if (text && text.trim()) {
            // 直接处理系统剪贴板数据，避免循环依赖
            if (text.includes('\t')) {
              // 看起来像Excel数据，直接处理
              try {
                const lines = text.trim().split('\n');
                const newRows = [];
                
                lines.forEach((line, index) => {
                  const columns = line.split('\t');
                  if (columns.length > 0 && columns[0] && columns[0].trim()) {
                    const newRow = createNewRow();
                    
                    // 映射列数据
                    COLUMNS_CONFIG.forEach((col, colIndex) => {
                      if (col.key !== 'id' && columns[colIndex] !== undefined) {
                        let value = columns[colIndex].trim();
                        if (col.type === 'number' && value) {
                          value = parseFloat(value) || 0;
                        }
                        newRow[col.key] = value;
                      }
                    });
                    
                    newRows.push(newRow);
                  }
                });
                
                if (newRows.length > 0) {
                  const newData = [...data, ...newRows];
                  addToHistory(newData);
                  setData(newData);
                  setHasUnsavedChanges(true);
                  scheduleAutosave();
                  message.success(`成功粘贴 ${newRows.length} 行Excel数据`);
                } else {
                  message.warning('未检测到有效的Excel数据');
                }
              } catch (error) {
                message.error('粘贴失败，请检查数据格式');

              }
            } else {
              // 普通文本，尝试粘贴到选中单元格
              if (selectedCells.size > 0) {
                updateSelectedCells(text);
              } else {
                message.warning('请先选择要粘贴的位置');
              }
            }
          } else {
            message.warning('剪贴板为空或无法访问');
          }
        }).catch(() => {
          message.warning('内部剪贴板为空，请先复制数据或使用 Ctrl+V');
        });
        return;
      } else {
        message.warning('内部剪贴板为空，请先复制数据');
        return;
      }
    }
    
    if (selectedCells.size === 0) {
      message.warning('请先选择要粘贴的位置');
      return;
    }
    
    const newData = [...data];
    let pasteCount = 0;
    
    // 简单的粘贴逻辑：将复制的第一个值粘贴到所有选中单元格
    const firstValue = copyBuffer[0] && copyBuffer[0].value;
    if (firstValue !== undefined) {
      selectedCells.forEach(cellId => {
        const [rowId, columnKey] = cellId.split('-');
        const rowIndex = newData.findIndex(row => row.id === rowId);
        if (rowIndex !== -1) {
          const column = COLUMNS_CONFIG.find(col => col.key === columnKey);
          if (column && column.editable !== false) {
            let processedValue = firstValue;
            if (column && column.type === 'number') {
              processedValue = firstValue === '' ? 0 : parseFloat(firstValue) || 0;
            }
            newData[rowIndex] = {
              ...newData[rowIndex],
              [columnKey]: processedValue,
              _modified: true
            };
            pasteCount++;
          }
        }
      });
    }
    
    if (pasteCount > 0) {
      addToHistory(newData);
      setData(newData);
      setHasUnsavedChanges(true);
      scheduleAutosave();
      message.success(`已粘贴到 ${pasteCount} 个单元格`);
    }
  }, [data, copyBuffer, selectedCells, updateSelectedCells, addToHistory, scheduleAutosave]);

  // 导出Excel
  const exportToExcel = useCallback(() => {
    // 构造CSV格式数据
    const headers = COLUMNS_CONFIG.map(col => col.title).join(',');
    const rows = filteredData.map(row => 
      COLUMNS_CONFIG.map(col => row[col.key] || '').join(',')
    );
    const csvContent = [headers, ...rows].join('\n');
    
    // 创建下载链接
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${modelName}_data_${new Date().toISOString().slice(0, 10)}.csv`;
    link.click();
    
    message.success('数据已导出');
  }, [filteredData, modelName]);

  // 单元格选择逻辑
  const handleCellClick = useCallback((e, rowId, columnKey) => {
    const cellId = `${rowId}-${columnKey}`;
    
    setActiveCell(cellId);
    
    if (e.ctrlKey || e.metaKey) {
      // Ctrl+点击：多选
      const newSelected = new Set(selectedCells);
      if (newSelected.has(cellId)) {
        newSelected.delete(cellId);
      } else {
        newSelected.add(cellId);
      }
      setSelectedCells(newSelected);
    } else if (e.shiftKey && selectedCells.size > 0) {
      // Shift+点击：范围选择
      const startCell = parseCellId(Array.from(selectedCells)[0]);
      const endCell = parseCellId(cellId);
      
      if (startCell && endCell) {
        const newSelected = new Set();
        const minRow = Math.min(startCell.rowIndex, endCell.rowIndex);
        const maxRow = Math.max(startCell.rowIndex, endCell.rowIndex);
        const minCol = Math.min(startCell.columnIndex, endCell.columnIndex);
        const maxCol = Math.max(startCell.columnIndex, endCell.columnIndex);
        
        for (let rowIdx = minRow; rowIdx <= maxRow; rowIdx++) {
          for (let colIdx = minCol; colIdx <= maxCol; colIdx++) {
            const targetRow = filteredData[rowIdx];
            const targetColumn = COLUMNS_CONFIG[colIdx];
            if (targetRow && targetColumn) {
              newSelected.add(`${targetRow.id}-${targetColumn.key}`);
            }
          }
        }
        
        setSelectedCells(newSelected);
      }
    } else {
      // 普通点击：单选
      setSelectedCells(new Set([cellId]));
    }
  }, [selectedCells, parseCellId, filteredData]);

  // 右键菜单
  const handleCellRightClick = useCallback((e, rowId, columnKey) => {
    e.preventDefault();
    const cellId = `${rowId}-${columnKey}`;
    
    if (!selectedCells.has(cellId)) {
      setSelectedCells(new Set([cellId]));
      setActiveCell(cellId);
    }
    
    setContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      cellId
    });
  }, [selectedCells]);

  // 双击编辑
  const handleCellDoubleClick = useCallback((rowId, columnKey, currentValue) => {
    const column = COLUMNS_CONFIG.find(col => col.key === columnKey);
    if (column && column.editable === false) return;
    
    setEditingCell({ rowId, columnKey });
    setEditValue(currentValue ? currentValue.toString() : '');
    
    // 延迟聚焦输入框
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus();
        editInputRef.current.select();
      }
    }, 50);
  }, []);

  // 完成编辑
  const finishEditing = useCallback(() => {
    if (editingCell) {
      updateCell(editingCell.rowId, editingCell.columnKey, editValue);
      setEditingCell(null);
      setEditValue('');
    }
  }, [editingCell, editValue, updateCell]);

  // 取消编辑
  const cancelEditing = useCallback(() => {
    setEditingCell(null);
    setEditValue('');
  }, []);

  // 排序功能
  const handleSort = useCallback((columnKey) => {
    let direction = 'asc';
    if (sortConfig.key === columnKey && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    
    setSortConfig({ key: columnKey, direction });
    
    // 找到列配置
    const column = COLUMNS_CONFIG.find(col => col.key === columnKey);
    
    const sortedData = [...data].sort((a, b) => {
      let aVal = a[columnKey];
      let bVal = b[columnKey];
      
      // 处理空值
      if (aVal == null && bVal == null) return 0;
      if (aVal == null) return direction === 'asc' ? 1 : -1;
      if (bVal == null) return direction === 'asc' ? -1 : 1;
      
      // 根据列类型进行排序
      if (column && column.type === 'number') {
        // 数字排序
        aVal = parseFloat(aVal) || 0;
        bVal = parseFloat(bVal) || 0;
      } else {
        // 字符串排序（不区分大小写）
        aVal = String(aVal).toLowerCase();
        bVal = String(bVal).toLowerCase();
      }
      
      if (aVal === bVal) return 0;
      
      const result = aVal < bVal ? -1 : 1;
      return direction === 'asc' ? result : -result;
    });
    
    setData(sortedData);
  }, [data, sortConfig]);

  // 数据验证函数
  const validateRowData = (row, index) => {
    const errors = [];

    // 必填字段验证
    if (!row.filename || !row.filename.toString().trim()) {
      errors.push(`第${index + 1}行：文件名不能为空`);
    }

    // 数值范围验证
    const numericFields = {
      success_requests: { name: '成功请求数', min: 0, max: 1000000 },
      benchmark_duration: { name: '基准时长', min: 0, max: 86400 },
      input_tokens: { name: '输入Token总数', min: 0, max: 10000000 },
      output_tokens: { name: '生成Token总数', min: 0, max: 10000000 },
      request_throughput: { name: '请求吞吐量', min: 0, max: 10000 },
      output_token_throughput: { name: '输出Token吞吐量', min: 0, max: 100000 },
      total_token_throughput: { name: '总Token吞吐量', min: 0, max: 100000 },
      avg_ttft: { name: '平均TTFT', min: 0, max: 60000 },
      median_ttft: { name: '中位TTFT', min: 0, max: 60000 },
      p99_ttft: { name: 'P99 TTFT', min: 0, max: 60000 },
      avg_tpot: { name: '平均TPOT', min: 0, max: 10000 },
      median_tpot: { name: '中位TPOT', min: 0, max: 10000 },
      p99_tpot: { name: 'P99 TPOT', min: 0, max: 10000 }
    };

    Object.entries(numericFields).forEach(([field, config]) => {
      const value = parseFloat(row[field]) || 0;
      if (value < config.min || value > config.max) {
        errors.push(`第${index + 1}行：${config.name}必须在${config.min}到${config.max}之间`);
      }
    });

    // 文件名格式验证
    if (row.filename) {
      const filename = row.filename.toString().trim();
      if (filename.length > 256) {
        errors.push(`第${index + 1}行：文件名长度不能超过256个字符`);
      }
      if (!/^[a-zA-Z0-9._-]+$/.test(filename)) {
        errors.push(`第${index + 1}行：文件名只能包含字母、数字、点、下划线和连字符`);
      }
    }

    return errors;
  };

  // 保存数据到服务器
  const saveData = useCallback(async (silent = false) => {
    const modifiedRows = data.filter(row => row._modified || row._isNew);

    if (modifiedRows.length === 0) {
      if (!silent) message.warning('没有需要保存的数据');
      return;
    }

    // 数据验证
    const allErrors = [];
    modifiedRows.forEach((row, index) => {
      const errors = validateRowData(row, index);
      allErrors.push(...errors);
    });

    if (allErrors.length > 0) {
      if (!silent) {
        message.error(`数据验证失败：${allErrors.slice(0, 3).join('；')}${allErrors.length > 3 ? '等' : ''}`);
      }
      return;
    }

    setLoading(true);
    try {
      // 分离新增和更新的数据
      const newRows = modifiedRows.filter(row => row._isNew);
      const updateRows = modifiedRows.filter(row => !row._isNew);

      let successCount = 0;
      let totalErrors = [];

      // 批量创建新数据
      if (newRows.length > 0) {
        try {
          const createData = newRows.map(row => ({
            model_name: modelName,
            filename: (row.filename || '').toString().trim(),
            success_requests: Math.max(0, parseInt(row.success_requests) || 0),
            benchmark_duration: Math.max(0, parseFloat(row.benchmark_duration) || 0),
            input_tokens: Math.max(0, parseInt(row.input_tokens) || 0),
            output_tokens: Math.max(0, parseInt(row.output_tokens) || 0),
            request_throughput: Math.max(0, parseFloat(row.request_throughput) || 0),
            output_token_throughput: Math.max(0, parseFloat(row.output_token_throughput) || 0),
            total_token_throughput: Math.max(0, parseFloat(row.total_token_throughput) || 0),
            avg_ttft: Math.max(0, parseFloat(row.avg_ttft) || 0),
            median_ttft: Math.max(0, parseFloat(row.median_ttft) || 0),
            p99_ttft: Math.max(0, parseFloat(row.p99_ttft) || 0),
            avg_tpot: Math.max(0, parseFloat(row.avg_tpot) || 0),
            median_tpot: Math.max(0, parseFloat(row.median_tpot) || 0),
            p99_tpot: Math.max(0, parseFloat(row.p99_tpot) || 0)
          }));

          const createResponse = await http.post('/api/model-storage/performance-test-data/batch/', {
            data: createData
          });

          if (createResponse && Array.isArray(createResponse)) {
            // 更新本地数据的ID
            createResponse.forEach((newRecord, index) => {
              if (newRecord.id && newRows[index]) {
                newRows[index].id = newRecord.id;
                newRows[index]._isNew = false;
                newRows[index]._modified = false;
              }
            });
            successCount += createResponse.length;
          } else if (createResponse && createResponse.created) {
            // 处理部分成功的情况
            createResponse.created.forEach((newRecord, index) => {
              const matchingRow = newRows.find(row =>
                row.filename === newRecord.filename
              );
              if (matchingRow) {
                matchingRow.id = newRecord.id;
                matchingRow._isNew = false;
                matchingRow._modified = false;
              }
            });
            successCount += createResponse.created.length;
            if (createResponse.errors) {
              totalErrors.push(...createResponse.errors);
            }
          }
        } catch (error) {

          // 如果批量创建失败，回退到逐个创建
          for (const row of newRows) {
            try {
              const saveData = {
                model_name: modelName,
                filename: (row.filename || '').toString().trim(),
                success_requests: Math.max(0, parseInt(row.success_requests) || 0),
                benchmark_duration: Math.max(0, parseFloat(row.benchmark_duration) || 0),
                input_tokens: Math.max(0, parseInt(row.input_tokens) || 0),
                output_tokens: Math.max(0, parseInt(row.output_tokens) || 0),
                request_throughput: Math.max(0, parseFloat(row.request_throughput) || 0),
                output_token_throughput: Math.max(0, parseFloat(row.output_token_throughput) || 0),
                total_token_throughput: Math.max(0, parseFloat(row.total_token_throughput) || 0),
                avg_ttft: Math.max(0, parseFloat(row.avg_ttft) || 0),
                median_ttft: Math.max(0, parseFloat(row.median_ttft) || 0),
                p99_ttft: Math.max(0, parseFloat(row.p99_ttft) || 0),
                avg_tpot: Math.max(0, parseFloat(row.avg_tpot) || 0),
                median_tpot: Math.max(0, parseFloat(row.median_tpot) || 0),
                p99_tpot: Math.max(0, parseFloat(row.p99_tpot) || 0)
              };
              const response = await http.post('/api/model-storage/performance-test-data/', saveData);
              row.id = response.id;
              row._isNew = false;
              row._modified = false;
              successCount++;
            } catch (singleError) {

              totalErrors.push(`创建数据失败: ${singleError.message || '未知错误'}`);
            }
          }
        }
      }

      // 批量更新现有数据
      if (updateRows.length > 0) {
        try {
          const updateData = updateRows.map(row => ({
            id: row.id,
            model_name: modelName,
            filename: (row.filename || '').toString().trim(),
            success_requests: Math.max(0, parseInt(row.success_requests) || 0),
            benchmark_duration: Math.max(0, parseFloat(row.benchmark_duration) || 0),
            input_tokens: Math.max(0, parseInt(row.input_tokens) || 0),
            output_tokens: Math.max(0, parseInt(row.output_tokens) || 0),
            request_throughput: Math.max(0, parseFloat(row.request_throughput) || 0),
            output_token_throughput: Math.max(0, parseFloat(row.output_token_throughput) || 0),
            total_token_throughput: Math.max(0, parseFloat(row.total_token_throughput) || 0),
            avg_ttft: Math.max(0, parseFloat(row.avg_ttft) || 0),
            median_ttft: Math.max(0, parseFloat(row.median_ttft) || 0),
            p99_ttft: Math.max(0, parseFloat(row.p99_ttft) || 0),
            avg_tpot: Math.max(0, parseFloat(row.avg_tpot) || 0),
            median_tpot: Math.max(0, parseFloat(row.median_tpot) || 0),
            p99_tpot: Math.max(0, parseFloat(row.p99_tpot) || 0)
          }));

          const updateResponse = await http.put('/api/model-storage/performance-test-data/batch/', {
            data: updateData
          });

          if (updateResponse && updateResponse.updated) {
            updateRows.forEach(row => {
              row._modified = false;
            });
            successCount += updateResponse.updated.length;
            if (updateResponse.errors) {
              totalErrors.push(...updateResponse.errors);
            }
          } else {
            // 简单成功响应
            updateRows.forEach(row => {
              row._modified = false;
            });
            successCount += updateRows.length;
          }
        } catch (error) {

          // 如果批量更新失败，回退到逐个更新
          for (const row of updateRows) {
            try {
              const saveData = {
                model_name: modelName,
                filename: (row.filename || '').toString().trim(),
                success_requests: Math.max(0, parseInt(row.success_requests) || 0),
                benchmark_duration: Math.max(0, parseFloat(row.benchmark_duration) || 0),
                input_tokens: Math.max(0, parseInt(row.input_tokens) || 0),
                output_tokens: Math.max(0, parseInt(row.output_tokens) || 0),
                request_throughput: Math.max(0, parseFloat(row.request_throughput) || 0),
                output_token_throughput: Math.max(0, parseFloat(row.output_token_throughput) || 0),
                total_token_throughput: Math.max(0, parseFloat(row.total_token_throughput) || 0),
                avg_ttft: Math.max(0, parseFloat(row.avg_ttft) || 0),
                median_ttft: Math.max(0, parseFloat(row.median_ttft) || 0),
                p99_ttft: Math.max(0, parseFloat(row.p99_ttft) || 0),
                avg_tpot: Math.max(0, parseFloat(row.avg_tpot) || 0),
                median_tpot: Math.max(0, parseFloat(row.median_tpot) || 0),
                p99_tpot: Math.max(0, parseFloat(row.p99_tpot) || 0)
              };
              await http.put(`/api/model-storage/performance-test-data/${row.id}/`, saveData);
              row._modified = false;
              successCount++;
            } catch (singleError) {

              totalErrors.push(`更新数据失败: ${singleError.message || '未知错误'}`);
            }
          }
        }
      }

      // 显示结果消息和更新状态
      if (totalErrors.length === 0) {
        setHasUnsavedChanges(false);
        if (!silent) message.success(`✅ 批量保存成功，共保存 ${successCount} 条数据`);
      } else if (successCount > 0) {
        if (!silent) message.warning(`⚠️ 部分保存成功：${successCount} 条成功，${totalErrors.length} 条失败`);
      } else {
        if (!silent) message.error(`❌ 批量保存失败，所有数据都保存失败`);
      }

      // 刷新数据状态
      setData([...data]);

    } catch (error) {
      if (!silent) message.error('保存失败: ' + (error.message || '未知错误'));

    } finally {
      setLoading(false);
    }
  }, [data, modelName]);

  // 获取数据
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/model-storage/performance-test-data/', {
        params: { model_name: modelName }
      });

      const transformedData = response.map(item => ({
        ...item,
        id: item.id || generateId(),
        _modified: false,
        _isNew: false
      }));

      setData(transformedData);
      setHistory([transformedData]);
      setHistoryIndex(0);
      setHasUnsavedChanges(false);

    } catch (error) {

      // 使用示例数据
      const sampleData = [
        {
          id: 1,
          filename: 'test_model_v1.json',
          success_requests: 1000,
          benchmark_duration: 60.5,
          input_tokens: 50000,
          output_tokens: 25000,
          request_throughput: 16.53,
          output_token_throughput: 413.22,
          total_token_throughput: 1239.67,
          avg_ttft: 245.6,
          median_ttft: 230.1,
          p99_ttft: 450.8,
          avg_tpot: 12.3,
          median_tpot: 11.8,
          p99_tpot: 18.9,
          _modified: false,
          _isNew: false
        }
      ];

      setData(sampleData);
      setHistory([sampleData]);
      setHistoryIndex(0);
    } finally {

      setLoading(false);
    }
  }, [modelName]);

  // 应用过滤和排序
  useEffect(() => {
    let filtered = [...data];
    
    // 应用过滤器
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        filtered = filtered.filter(row => 
          row[key] && row[key].toString().toLowerCase().includes(value.toLowerCase())
        );
      }
    });
    
    setFilteredData(filtered);
  }, [data, filters]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e) => {
      // 编辑状态下的键盘事件
      if (editingCell) {
        switch (e.key) {
          case 'Enter':
            e.preventDefault();
            finishEditing();
            // 导航到下一行
            if (activeCell) {
              navigateCell('down', activeCell);
            }
            break;
          case 'Escape':
            e.preventDefault();
            cancelEditing();
            break;
          case 'Tab':
            e.preventDefault();
            finishEditing();
            // 导航到下一列
            if (activeCell) {
              navigateCell(e.shiftKey ? 'left' : 'right', activeCell);
            }
            break;
        }
        return;
      }
      
      // 全局快捷键
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault();
            saveData();
            break;
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              redo();
            } else {
              undo();
            }
            break;
          case 'c':
            if (selectedCells.size > 0) {
              e.preventDefault();
              copySelectedCells();
            }
            break;
          case 'v':
            e.preventDefault();
            // 直接使用 pasteCells，它已经包含了系统剪贴板的处理逻辑
            pasteCells();
            break;
          case 'n':
            e.preventDefault();
            addEmptyRow();
            break;
          case 'a':
            e.preventDefault();
            // 全选所有单元格
            const allCells = new Set();
            filteredData.forEach(row => {
              COLUMNS_CONFIG.forEach(col => {
                allCells.add(`${row.id}-${col.key}`);
              });
            });
            setSelectedCells(allCells);
            break;
        }
      }
      
      // 方向键导航
      if (activeCell && !editingCell) {
        switch (e.key) {
          case 'ArrowUp':
            e.preventDefault();
            navigateCell('up', activeCell);
            break;
          case 'ArrowDown':
            e.preventDefault();
            navigateCell('down', activeCell);
            break;
          case 'ArrowLeft':
            e.preventDefault();
            navigateCell('left', activeCell);
            break;
          case 'ArrowRight':
            e.preventDefault();
            navigateCell('right', activeCell);
            break;
          case 'Enter':
            if (activeCell) {
              e.preventDefault();
              const [rowId, columnKey] = activeCell.split('-');
              const row = filteredData.find(r => r.id === rowId);
              handleCellDoubleClick(rowId, columnKey, row && row[columnKey]);
            }
            break;
        }
      }
      
      // 删除键
      if (e.key === 'Delete' && selectedRows.size > 0 && !editingCell) {
        deleteSelectedRows();
      }
      
      // F2 编辑当前单元格
      if (e.key === 'F2' && activeCell && !editingCell) {
        e.preventDefault();
        const [rowId, columnKey] = activeCell.split('-');
        const row = filteredData.find(r => r.id === rowId);
        handleCellDoubleClick(rowId, columnKey, row && row[columnKey]);
      }
    };
    
    // 简单的粘贴处理
    const handlePaste = (e) => {
      if (!e.target.closest('.excel-table-container')) return;
      e.preventDefault();
      pasteCells(); // 直接调用我们的粘贴函数
    };
    
    // 隐藏右键菜单
    const handleClickOutside = () => {
      setContextMenu({ ...contextMenu, visible: false });
    };
    
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('paste', handlePaste);
    document.addEventListener('click', handleClickOutside);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('paste', handlePaste);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [
    editingCell, activeCell, selectedCells, selectedRows, filteredData,
    saveData, undo, redo, copySelectedCells, pasteCells, addEmptyRow,
    finishEditing, cancelEditing, deleteSelectedRows, navigateCell,
    handleCellDoubleClick, contextMenu
  ]);

  // 初始化数据
  useEffect(() => {
    if (modelName) {
      fetchData();
    }
  }, [modelName]); // 移除fetchData依赖，避免无限循环

  // 清理自动保存定时器
  useEffect(() => {
    return () => {
      if (autosaveTimeoutRef.current) {
        clearTimeout(autosaveTimeoutRef.current);
      }
    };
  }, []);

  // 渲染表格单元格
  const renderCell = useCallback((row, column) => {
    const cellId = `${row.id}-${column.key}`;
    const isSelected = selectedCells.has(cellId);
    const isActive = activeCell === cellId;
    const isEditing = editingCell && editingCell.rowId === row.id && editingCell.columnKey === column.key;
    const value = row[column.key];
    
    return (
      <div
        key={cellId}
        data-cell-id={cellId}
        className={`excel-cell ${isSelected ? 'selected' : ''} ${isActive ? 'active' : ''} ${isEditing ? 'editing' : ''}`}
        style={{ 
          width: column.width,
          minWidth: column.width,
          maxWidth: column.width,
        }}
        data-type={column.type}
        data-fixed={column.fixed}
        onClick={(e) => handleCellClick(e, row.id, column.key)}
        onContextMenu={(e) => handleCellRightClick(e, row.id, column.key)}
        onDoubleClick={() => handleCellDoubleClick(row.id, column.key, value)}
      >
        {isEditing ? (
          <input
            ref={editInputRef}
            type={column.type === 'number' ? 'number' : 'text'}
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onBlur={finishEditing}
            onKeyDown={(e) => e.stopPropagation()}
            className="excel-cell-input"
            step={column.step}
          />
        ) : (
          <span className="excel-cell-content">
            {column.type === 'number' && typeof value === 'number' 
              ? value.toLocaleString() 
              : value || ''
            }
          </span>
        )}
      </div>
    );
  }, [selectedCells, activeCell, editingCell, editValue, handleCellClick, handleCellRightClick, handleCellDoubleClick, finishEditing]);

  // 渲染表格行
  const renderRow = useCallback((row, rowIndex) => {
    const isRowSelected = selectedRows.has(row.id);
    
    return (
      <div
        key={row.id}
        className={`excel-row ${isRowSelected ? 'selected' : ''} ${row._modified ? 'modified' : ''}`}
        style={{ display: 'flex' }}
      >
        {/* 行号 */}
        <div 
          className="excel-row-number"
          onClick={(e) => {
            if (e.ctrlKey) {
              const newSelected = new Set(selectedRows);
              if (newSelected.has(row.id)) {
                newSelected.delete(row.id);
              } else {
                newSelected.add(row.id);
              }
              setSelectedRows(newSelected);
            } else {
              setSelectedRows(new Set([row.id]));
              // 选中整行的所有单元格
              const rowCells = new Set();
              COLUMNS_CONFIG.forEach(col => {
                rowCells.add(`${row.id}-${col.key}`);
              });
              setSelectedCells(rowCells);
            }
          }}
        >
          {rowIndex + 1}
        </div>
        
        {/* 数据单元格 */}
        {COLUMNS_CONFIG.map(column => renderCell(row, column))}
      </div>
    );
  }, [selectedRows, renderCell]);

  // 右键菜单内容
  const contextMenuItems = [
    {
      key: 'copy',
      label: '复制',
      icon: <CopyOutlined />,
      onClick: copySelectedCells,
      disabled: selectedCells.size === 0
    },
    {
      key: 'paste',
      label: '粘贴',
      onClick: pasteCells,
      disabled: !copyBuffer || selectedCells.size === 0
    },
    { type: 'divider' },
    {
      key: 'clear',
      label: '清空内容',
      onClick: () => updateSelectedCells(''),
      disabled: selectedCells.size === 0
    },
    {
      key: 'fill-down',
      label: '向下填充',
      onClick: () => {
        if (activeCell) {
          const activeParsed = parseCellId(activeCell);
          if (activeParsed && activeParsed.rowIndex < filteredData.length - 1) {
            const endCellId = `${filteredData[filteredData.length - 1].id}-${activeParsed.columnKey}`;
            handleDragFill(activeCell, endCellId);
          }
        }
      },
      disabled: !activeCell
    },
    { type: 'divider' },
    {
      key: 'insert-row',
      label: '插入行',
      icon: <PlusOutlined />,
      onClick: addEmptyRow
    },
    {
      key: 'delete-row',
      label: '删除行',
      icon: <DeleteOutlined />,
      onClick: deleteSelectedRows,
      disabled: selectedRows.size === 0,
      danger: true
    }
  ];

  // 批量操作菜单
  const batchActionsMenu = (
    <div className="batch-actions-menu">
      <Button 
        type="text" 
        size="small" 
        onClick={() => updateSelectedCells('')}
        disabled={selectedCells.size === 0}
      >
        清空选中单元格
      </Button>
      <Button 
        type="text" 
        size="small" 
        onClick={copySelectedCells}
        disabled={selectedCells.size === 0}
      >
        复制选中单元格
      </Button>
      <Button 
        type="text" 
        size="small" 
        onClick={pasteCells}
        disabled={!copyBuffer || selectedCells.size === 0}
      >
        粘贴
      </Button>
      <Button 
        type="text" 
        size="small" 
        onClick={() => {
          // 测试粘贴功能 - 直接调用 pasteCells
          if (navigator.clipboard && navigator.clipboard.readText) {
            navigator.clipboard.readText().then(text => {
              message.info(`剪贴板内容预览: ${text.slice(0, 50)}...`);
              pasteCells(); // 直接调用 pasteCells
            }).catch(err => {
              message.error('无法访问剪贴板: ' + err.message);
            });
          } else {
            message.warning('浏览器不支持剪贴板 API');
          }
        }}
      >
        测试粘贴
      </Button>
      <Button 
        type="text" 
        size="small" 
        onClick={() => addMultipleRows(5)}
      >
        批量添加5行
      </Button>
      <Button 
        type="text" 
        size="small" 
        onClick={() => addMultipleRows(10)}
      >
        批量添加10行
      </Button>
      <Button 
        type="text" 
        size="small" 
        onClick={() => {
          // 生成测试Excel数据并复制到剪贴板
          const testData = [
            "test1.json\t100\t60.5\t5000\t2500\t16.53\t413.22\t1239.67\t245.6\t230.1\t450.8\t12.3\t11.8\t18.9",
            "test2.json\t200\t120.0\t10000\t5000\t33.06\t826.44\t2479.34\t190.2\t185.5\t320.1\t10.1\t9.8\t15.2",
            "test3.json\t150\t90.75\t7500\t3750\t24.80\t619.83\t1859.51\t220.8\t215.3\t380.4\t11.2\t10.9\t16.8"
          ].join('\n');
          
          if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(testData).then(() => {
              message.success('测试数据已复制到剪贴板，现在可以用 Ctrl+V 粘贴测试！');
            }).catch(() => {
              message.warning('无法复制到剪贴板，请手动复制测试数据');

            });
          } else {
            message.warning('浏览器不支持剪贴板 API');

          }
        }}
      >
        生成测试数据
      </Button>
      <Button 
        type="text" 
        size="small" 
        danger
        onClick={deleteSelectedRows}
        disabled={selectedRows.size === 0}
      >
        删除选中行
      </Button>
    </div>
  );

  return (
    <div className="excel-table-page">
      <Card
        title={
          <div className="page-header">
            <FileExcelOutlined className="page-icon" />
            <span>高性能Excel表格 - {decodeURIComponent(modelName || '')}</span>
            {hasUnsavedChanges && <span className="unsaved-indicator">●</span>}
          </div>
        }
        extra={
          <Space size="small">
            {/* 状态信息 */}
            <div className="status-info">
              {selectedCells.size > 0 && (
                <span className="selection-info">
                  已选中 {selectedCells.size} 个单元格
                </span>
              )}
              {selectedRows.size > 0 && (
                <span className="selection-info">
                  已选中 {selectedRows.size} 行
                </span>
              )}
              {filteredData.length > 0 && (
                <span className="selection-info">
                  共 {filteredData.length} 行数据
                </span>
              )}
            </div>
            
            {/* 操作按钮 */}
            <Tooltip title="撤销 (Ctrl+Z)">
              <Button 
                size="small"
                icon={<UndoOutlined />} 
                onClick={undo}
                disabled={historyIndex <= 0}
              />
            </Tooltip>
            
            <Tooltip title="重做 (Ctrl+Shift+Z)">
              <Button 
                size="small"
                icon={<RedoOutlined />} 
                onClick={redo}
                disabled={historyIndex >= history.length - 1}
              />
            </Tooltip>
            
            <Tooltip title="复制 (Ctrl+C)">
              <Button 
                size="small"
                icon={<CopyOutlined />} 
                onClick={copySelectedCells}
                disabled={selectedCells.size === 0}
              />
            </Tooltip>
            
            <Tooltip title="导出Excel">
              <Button 
                size="small"
                icon={<ExportOutlined />} 
                onClick={exportToExcel}
                disabled={filteredData.length === 0}
              />
            </Tooltip>
            
            <Tooltip title="添加行 (Ctrl+N)">
              <Button 
                size="small"
                icon={<PlusOutlined />} 
                onClick={addEmptyRow}
              />
            </Tooltip>
            
            <Tooltip title="刷新数据">
              <Button 
                size="small"
                icon={<ReloadOutlined />} 
                onClick={fetchData}
                loading={loading}
              />
            </Tooltip>
            
            <Tooltip title="保存 (Ctrl+S)">
              <Button 
                type="primary"
                size="small"
                icon={<SaveOutlined />} 
                onClick={() => saveData()}
                loading={loading}
                disabled={!hasUnsavedChanges}
              >
                保存 {hasUnsavedChanges && `(${data.filter(r => r._modified || r._isNew).length})`}
              </Button>
            </Tooltip>
            
            <Popover content={batchActionsMenu} title="批量操作" trigger="click">
              <Button 
                size="small"
                icon={<MoreOutlined />}
              />
            </Popover>
          </Space>
        }
      >
        {/* 帮助信息 */}
        <div className="help-info">
          <div className="help-shortcuts">
            <span><kbd>Ctrl+V</kbd> Excel粘贴</span>
            <span><kbd>Ctrl+C</kbd> 复制</span>
            <span><kbd>Ctrl+S</kbd> 保存</span>
            <span><kbd>Ctrl+Z</kbd> 撤销</span>
            <span><kbd>F2</kbd> 编辑</span>
            <span><kbd>方向键</kbd> 导航</span>
            <span><kbd>Enter</kbd> 确认编辑</span>
            <span><kbd>Tab</kbd> 下一列</span>
            <span><kbd>Delete</kbd> 删除行</span>
            <span><kbd>右键</kbd> 菜单</span>
          </div>
          <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
            💡 提示：先选择一个单元格，然后使用 Ctrl+V 粘贴 Excel 数据，或直接在表格区域内粘贴自动添加新行
          </div>
        </div>
        
        {/* Excel表格容器 */}
        <div 
          ref={containerRef}
          className="excel-table-container"
        >
          {/* 表格头部 */}
          <div className="excel-header">
            <div className="excel-corner"></div>
            {COLUMNS_CONFIG.map(column => (
              <div
                key={column.key}
                className={`excel-header-cell ${sortConfig.key === column.key ? 'sorted' : ''}`}
                style={{ 
                  width: column.width,
                  minWidth: column.width,
                  maxWidth: column.width,
                }}
                data-fixed={column.fixed}
                onClick={() => handleSort(column.key)}
              >
                <span>{column.title}</span>
                {column.required && <span className="required-mark">*</span>}
                {sortConfig.key === column.key && (
                  <span className="sort-icon">
                    {sortConfig.direction === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </div>
            ))}
          </div>
          
          {/* 表格主体 */}
          <div 
            ref={tableRef}
            className="excel-body"
          >
            {loading ? (
              <div className="loading-container">
                <div className="loading-spinner">加载中...</div>
              </div>
            ) : (
              filteredData.map((row, index) => renderRow(row, index))
            )}
          </div>
        </div>
      </Card>
      
      {/* 右键菜单 */}
      {contextMenu.visible && (
        <Dropdown
          open={contextMenu.visible}
          menu={{ items: contextMenuItems }}
          trigger={[]}
        >
          <div 
            style={{
              position: 'fixed',
              left: contextMenu.x,
              top: contextMenu.y,
              width: 1,
              height: 1,
              zIndex: 1000
            }}
          />
        </Dropdown>
      )}
    </div>
  );
}

export default ModelDataTableExcel; 