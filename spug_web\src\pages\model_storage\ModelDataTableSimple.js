import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Button, message, Table, Space, Modal, Input, Form, Select, Tooltip } from 'antd';
import { EditOutlined, ReloadOutlined, DeleteOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import http from 'libs/http';

const { Option } = Select;

function ModelDataTableSimple() {
  const { modelName } = useParams();
  const [loading, setLoading] = useState(true);
  const [modelData, setModelData] = useState([]);
  const [editingKey, setEditingKey] = useState('');
  const [addModalVisible, setAddModalVisible] = useState(false);

  // 添加样式
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .modified-row {
        background-color: #fff7e6 !important;
        border-left: 3px solid #faad14 !important;
      }
      .modified-row:hover {
        background-color: #fff1b8 !important;
      }
      .editing-row {
        background-color: #e6f7ff !important;
        border-left: 3px solid #1890ff !important;
      }
      .editing-row:hover {
        background-color: #bae7ff !important;
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const tableRef = useRef(null);

  // 判断是否正在编辑
  const isEditing = (record) => record.key === editingKey;

  // 编辑操作
  const edit = useCallback((record) => {


    // 防止重复编辑
    if (editingKey !== '') {
      message.warning('请先完成当前编辑');
      return;
    }

    editForm.setFieldsValue({
      filename: record.filename || '',
      success_requests: record.success_requests !== null && record.success_requests !== undefined ? record.success_requests : '',
      benchmark_duration: record.benchmark_duration !== null && record.benchmark_duration !== undefined ? record.benchmark_duration : '',
      input_tokens: record.input_tokens !== null && record.input_tokens !== undefined ? record.input_tokens : '',
      output_tokens: record.output_tokens !== null && record.output_tokens !== undefined ? record.output_tokens : '',
      request_throughput: record.request_throughput !== null && record.request_throughput !== undefined ? record.request_throughput : '',
      output_token_throughput: record.output_token_throughput !== null && record.output_token_throughput !== undefined ? record.output_token_throughput : '',
      total_token_throughput: record.total_token_throughput !== null && record.total_token_throughput !== undefined ? record.total_token_throughput : '',
      avg_ttft: record.avg_ttft !== null && record.avg_ttft !== undefined ? record.avg_ttft : '',
      median_ttft: record.median_ttft !== null && record.median_ttft !== undefined ? record.median_ttft : '',
      p99_ttft: record.p99_ttft !== null && record.p99_ttft !== undefined ? record.p99_ttft : '',
      avg_tpot: record.avg_tpot !== null && record.avg_tpot !== undefined ? record.avg_tpot : '',
      median_tpot: record.median_tpot !== null && record.median_tpot !== undefined ? record.median_tpot : '',
      p99_tpot: record.p99_tpot !== null && record.p99_tpot !== undefined ? record.p99_tpot : '',
    });
    setEditingKey(record.key);
  }, [editingKey, editForm]);

  // 取消编辑
  const cancel = () => {
    setEditingKey('');
  };

  // 删除记录
  const deleteRecord = (record) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文件 "${record.filename}" 的记录吗？`,
      onOk: async () => {
        try {
          // 尝试调用API删除
          try {
            await http.delete(`/api/model-storage/performance-test-data/${record.id}/`);
            message.success('删除成功');
          } catch (apiError) {
            // 如果API失败，从本地数据中删除
            message.success('删除成功（本地模式）');
          }

          // 从本地数据中移除
          const newData = modelData.filter(item => item.key !== record.key);
          setModelData(newData);

        } catch (error) {
          message.error('删除失败');

        }
      }
    });
  };



  // 快速添加空行
  const addEmptyRow = () => {
    // 获取当前数据中最大的数字ID，然后+1
    const maxId = Math.max(
      ...modelData
        .map(row => parseInt(row.id))
        .filter(id => !isNaN(id)),
      0
    );
    const newId = maxId + 1;

    const newRecord = {
      key: newId, // 使用数字key
      id: newId, // 使用数字ID
      filename: '',
      success_requests: null,
      benchmark_duration: null,
      input_tokens: null,
      output_tokens: null,
      request_throughput: null,
      output_token_throughput: null,
      total_token_throughput: null,
      avg_ttft: null,
      median_ttft: null,
      p99_ttft: null,
      avg_tpot: null,
      median_tpot: null,
      p99_tpot: null,
      _original: null // 标记为新记录
    };

    setModelData([...modelData, newRecord]);
    message.success('✅ 已添加空行，双击单元格可直接编辑');
  };

  // 更新本地数据（不保存到服务器）
  const updateLocalData = useCallback(async (key) => {

    try {
      const row = await editForm.validateFields();
      const newData = [...modelData];
      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];
        const updatedItem = { ...item, ...row, _modified: true }; // 标记为已修改
        newData.splice(index, 1, updatedItem);
        setModelData(newData);
        setEditingKey('');
        message.info('数据已更新，请点击"批量保存"按钮保存到服务器');
      }
    } catch (errInfo) {

      message.error('数据格式错误，请检查输入');
    }
  }, [editForm, modelData]);

  // 批量保存修改的数据
  const saveAllData = async () => {
    // 找出所有修改过的数据
    const modifiedData = modelData.filter(item => item._modified);

    if (modifiedData.length === 0) {
      message.warning('没有修改的数据需要保存');
      return;
    }

    try {
      setLoading(true);

      // 分离新增和更新的数据
      const newData = modifiedData.filter(item => !item.id || typeof item.id !== 'number');
      const updateData = modifiedData.filter(item => item.id && typeof item.id === 'number');

      let successCount = 0;
      let totalErrors = [];

      // 批量创建新数据
      if (newData.length > 0) {
        try {
          const createPayload = newData.map(item => ({
            model_name: item.model_name,
            filename: item.filename,
            success_requests: item.success_requests !== null && item.success_requests !== undefined ? Math.max(0, parseInt(item.success_requests) || 0) : 0,
            benchmark_duration: item.benchmark_duration !== null && item.benchmark_duration !== undefined ? Math.max(0, parseFloat(item.benchmark_duration) || 0) : 0,
            input_tokens: item.input_tokens !== null && item.input_tokens !== undefined ? Math.max(0, parseInt(item.input_tokens) || 0) : 0,
            output_tokens: item.output_tokens !== null && item.output_tokens !== undefined ? Math.max(0, parseInt(item.output_tokens) || 0) : 0,
            request_throughput: item.request_throughput !== null && item.request_throughput !== undefined ? Math.max(0, parseFloat(item.request_throughput) || 0) : 0,
            output_token_throughput: item.output_token_throughput !== null && item.output_token_throughput !== undefined ? Math.max(0, parseFloat(item.output_token_throughput) || 0) : 0,
            total_token_throughput: item.total_token_throughput !== null && item.total_token_throughput !== undefined ? Math.max(0, parseFloat(item.total_token_throughput) || 0) : 0,
            avg_ttft: item.avg_ttft !== null && item.avg_ttft !== undefined ? Math.max(0, parseFloat(item.avg_ttft) || 0) : 0,
            median_ttft: item.median_ttft !== null && item.median_ttft !== undefined ? Math.max(0, parseFloat(item.median_ttft) || 0) : 0,
            p99_ttft: item.p99_ttft !== null && item.p99_ttft !== undefined ? Math.max(0, parseFloat(item.p99_ttft) || 0) : 0,
            avg_tpot: item.avg_tpot !== null && item.avg_tpot !== undefined ? Math.max(0, parseFloat(item.avg_tpot) || 0) : 0,
            median_tpot: item.median_tpot !== null && item.median_tpot !== undefined ? Math.max(0, parseFloat(item.median_tpot) || 0) : 0,
            p99_tpot: item.p99_tpot !== null && item.p99_tpot !== undefined ? Math.max(0, parseFloat(item.p99_tpot) || 0) : 0,
          }));

          const createResponse = await http.post('/api/model-storage/performance-test-data/batch/', {
            data: createPayload
          });

          if (createResponse && Array.isArray(createResponse)) {
            // 更新本地数据的ID
            createResponse.forEach((newRecord, index) => {
              if (newRecord.id && newData[index]) {
                newData[index].id = newRecord.id;
              }
            });
            successCount += createResponse.length;
          } else if (createResponse && createResponse.created) {
            // 处理部分成功的情况
            createResponse.created.forEach((newRecord) => {
              const matchingItem = newData.find(item =>
                item.filename === newRecord.filename
              );
              if (matchingItem) {
                matchingItem.id = newRecord.id;
              }
            });
            successCount += createResponse.created.length;
            if (createResponse.errors) {
              totalErrors.push(...createResponse.errors);
            }
          }
        } catch (error) {

          // 如果批量创建失败，回退到逐个创建
          for (const item of newData) {
            try {
              await saveModelData(item);
              successCount++;
            } catch (singleError) {
              totalErrors.push(`创建数据失败: ${singleError.message || '未知错误'}`);
            }
          }
        }
      }

      // 批量更新现有数据
      if (updateData.length > 0) {
        try {
          const updatePayload = updateData.map(item => ({
            id: item.id,
            model_name: item.model_name,
            filename: item.filename,
            success_requests: item.success_requests !== null && item.success_requests !== undefined ? Math.max(0, parseInt(item.success_requests) || 0) : 0,
            benchmark_duration: item.benchmark_duration !== null && item.benchmark_duration !== undefined ? Math.max(0, parseFloat(item.benchmark_duration) || 0) : 0,
            input_tokens: item.input_tokens !== null && item.input_tokens !== undefined ? Math.max(0, parseInt(item.input_tokens) || 0) : 0,
            output_tokens: item.output_tokens !== null && item.output_tokens !== undefined ? Math.max(0, parseInt(item.output_tokens) || 0) : 0,
            request_throughput: item.request_throughput !== null && item.request_throughput !== undefined ? Math.max(0, parseFloat(item.request_throughput) || 0) : 0,
            output_token_throughput: item.output_token_throughput !== null && item.output_token_throughput !== undefined ? Math.max(0, parseFloat(item.output_token_throughput) || 0) : 0,
            total_token_throughput: item.total_token_throughput !== null && item.total_token_throughput !== undefined ? Math.max(0, parseFloat(item.total_token_throughput) || 0) : 0,
            avg_ttft: item.avg_ttft !== null && item.avg_ttft !== undefined ? Math.max(0, parseFloat(item.avg_ttft) || 0) : 0,
            median_ttft: item.median_ttft !== null && item.median_ttft !== undefined ? Math.max(0, parseFloat(item.median_ttft) || 0) : 0,
            p99_ttft: item.p99_ttft !== null && item.p99_ttft !== undefined ? Math.max(0, parseFloat(item.p99_ttft) || 0) : 0,
            avg_tpot: item.avg_tpot !== null && item.avg_tpot !== undefined ? Math.max(0, parseFloat(item.avg_tpot) || 0) : 0,
            median_tpot: item.median_tpot !== null && item.median_tpot !== undefined ? Math.max(0, parseFloat(item.median_tpot) || 0) : 0,
            p99_tpot: item.p99_tpot !== null && item.p99_tpot !== undefined ? Math.max(0, parseFloat(item.p99_tpot) || 0) : 0,
          }));

          const updateResponse = await http.put('/api/model-storage/performance-test-data/batch/', {
            data: updatePayload
          });

          if (updateResponse && updateResponse.updated) {
            successCount += updateResponse.updated.length;
            if (updateResponse.errors) {
              totalErrors.push(...updateResponse.errors);
            }
          } else {
            successCount += updateData.length;
          }
        } catch (error) {

          // 如果批量更新失败，回退到逐个更新
          for (const item of updateData) {
            try {
              await saveModelData(item);
              successCount++;
            } catch (singleError) {
              totalErrors.push(`更新数据失败: ${singleError.message || '未知错误'}`);
            }
          }
        }
      }

      // 显示结果并更新状态
      if (totalErrors.length === 0) {
        // 清除所有修改标记
        const newModelData = modelData.map(item => {
          if (item._modified) {
            const { _modified, ...cleanItem } = item;
            return cleanItem;
          }
          return item;
        });
        setModelData(newModelData);
        message.success(`✅ 批量保存成功，共保存 ${successCount} 条数据`);
      } else if (successCount > 0) {
        // 只清除成功保存的数据的修改标记
        const successfulItems = [...newData.slice(0, Math.min(successCount, newData.length)),
                                 ...updateData.slice(0, Math.max(0, successCount - newData.length))];
        const newModelData = modelData.map(item => {
          if (successfulItems.some(saved => saved.key === item.key)) {
            const { _modified, ...cleanItem } = item;
            return cleanItem;
          }
          return item;
        });
        setModelData(newModelData);
        message.warning(`⚠️ 部分保存成功：${successCount} 条成功，${totalErrors.length} 条失败`);
      } else {
        message.error(`❌ 批量保存失败，所有 ${totalErrors.length} 条记录都保存失败`);
      }
    } catch (error) {
      message.error('批量保存过程中发生错误: ' + (error.message || '未知错误'));

    } finally {
      setLoading(false);
    }
  };

  // 数据验证函数
  const validateRecord = (record) => {
    const errors = [];

    // 必填字段验证
    if (!record.filename || !record.filename.trim()) {
      errors.push('文件名不能为空');
    }

    // 数值范围验证
    const numericFields = {
      success_requests: { name: '成功请求数', min: 0, max: 1000000 },
      benchmark_duration: { name: '基准时长', min: 0, max: 86400 },
      input_tokens: { name: '输入Token总数', min: 0, max: 10000000 },
      output_tokens: { name: '生成Token总数', min: 0, max: 10000000 },
      request_throughput: { name: '请求吞吐量', min: 0, max: 10000 },
      output_token_throughput: { name: '输出Token吞吐量', min: 0, max: 100000 },
      total_token_throughput: { name: '总Token吞吐量', min: 0, max: 100000 },
      avg_ttft: { name: '平均TTFT', min: 0, max: 60000 },
      median_ttft: { name: '中位TTFT', min: 0, max: 60000 },
      p99_ttft: { name: 'P99 TTFT', min: 0, max: 60000 },
      avg_tpot: { name: '平均TPOT', min: 0, max: 10000 },
      median_tpot: { name: '中位TPOT', min: 0, max: 10000 },
      p99_tpot: { name: 'P99 TPOT', min: 0, max: 10000 }
    };

    Object.entries(numericFields).forEach(([field, config]) => {
      const value = parseFloat(record[field]) || 0;
      if (value < config.min || value > config.max) {
        errors.push(`${config.name}必须在${config.min}到${config.max}之间`);
      }
    });

    // 文件名格式验证
    if (record.filename) {
      const filename = record.filename.trim();
      if (filename.length > 256) {
        errors.push('文件名长度不能超过256个字符');
      }
      if (!/^[a-zA-Z0-9._-]+$/.test(filename)) {
        errors.push('文件名只能包含字母、数字、点、下划线和连字符');
      }
    }

    return errors;
  };

  // 保存性能测试数据API调用
  const saveModelData = async (record) => {
    try {
      // 检查记录是否有效
      if (!record) {
        throw new Error('记录不能为空');
      }

      // 数据验证
      const validationErrors = validateRecord(record);
      if (validationErrors.length > 0) {
        throw new Error(`数据验证失败：${validationErrors.join('；')}`);
      }

      const saveData = {
        model_name: modelName, // 添加模型名称
        filename: (record.filename || '').trim(),
        success_requests: record.success_requests !== null && record.success_requests !== undefined ? Math.max(0, parseInt(record.success_requests) || 0) : 0,
        benchmark_duration: record.benchmark_duration !== null && record.benchmark_duration !== undefined ? Math.max(0, parseFloat(record.benchmark_duration) || 0) : 0,
        input_tokens: record.input_tokens !== null && record.input_tokens !== undefined ? Math.max(0, parseInt(record.input_tokens) || 0) : 0,
        output_tokens: record.output_tokens !== null && record.output_tokens !== undefined ? Math.max(0, parseInt(record.output_tokens) || 0) : 0,
        request_throughput: record.request_throughput !== null && record.request_throughput !== undefined ? Math.max(0, parseFloat(record.request_throughput) || 0) : 0,
        output_token_throughput: record.output_token_throughput !== null && record.output_token_throughput !== undefined ? Math.max(0, parseFloat(record.output_token_throughput) || 0) : 0,
        total_token_throughput: record.total_token_throughput !== null && record.total_token_throughput !== undefined ? Math.max(0, parseFloat(record.total_token_throughput) || 0) : 0,
        avg_ttft: record.avg_ttft !== null && record.avg_ttft !== undefined ? Math.max(0, parseFloat(record.avg_ttft) || 0) : 0,
        median_ttft: record.median_ttft !== null && record.median_ttft !== undefined ? Math.max(0, parseFloat(record.median_ttft) || 0) : 0,
        p99_ttft: record.p99_ttft !== null && record.p99_ttft !== undefined ? Math.max(0, parseFloat(record.p99_ttft) || 0) : 0,
        avg_tpot: record.avg_tpot !== null && record.avg_tpot !== undefined ? Math.max(0, parseFloat(record.avg_tpot) || 0) : 0,
        median_tpot: record.median_tpot !== null && record.median_tpot !== undefined ? Math.max(0, parseFloat(record.median_tpot) || 0) : 0,
        p99_tpot: record.p99_tpot !== null && record.p99_tpot !== undefined ? Math.max(0, parseFloat(record.p99_tpot) || 0) : 0,
      };

      // 如果有数据库ID且不是临时key，则更新；否则创建新记录
      if (record.id && typeof record.id === 'number' && record.id !== record.key) {
        await http.put(`/api/model-storage/performance-test-data/${record.id}/`, saveData);
      } else {
        const response = await http.post('/api/model-storage/performance-test-data/', saveData);
        // 更新本地记录的ID
        if (response && response.id) {
          record.id = response.id;
        }
      }
    } catch (error) {
      console.error('保存数据失败:', error);
      throw error;
    }
  };

  // 可编辑单元格组件 - Excel风格
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    // 根据字段类型确定输入组件类型
    const isNumberField = [
      'success_requests', 'benchmark_duration', 'input_tokens', 'output_tokens',
      'request_throughput', 'output_token_throughput', 'total_token_throughput',
      'avg_ttft', 'median_ttft', 'p99_ttft', 'avg_tpot', 'median_tpot', 'p99_tpot'
    ].includes(dataIndex);

    const inputNode = isNumberField ?
      <Input
        type="number"
        step="0.01"
        style={{
          width: '100%',
          height: '100%',
          padding: '2px 4px',
          fontSize: '12px',
          border: 'none',
          borderRadius: '0',
          outline: 'none',
          boxShadow: 'none',
          background: 'transparent'
        }}
        autoFocus
        onPressEnter={(e) => {
          e.preventDefault();
          // 只退出编辑模式，不保存
          setEditingKey('');
        }}
        onBlur={(e) => {
          // 只退出编辑模式，不保存
          setTimeout(() => setEditingKey(''), 100);
        }}
      /> :
      <Input
        style={{
          width: '100%',
          height: '100%',
          padding: '2px 4px',
          fontSize: '12px',
          border: 'none',
          borderRadius: '0',
          outline: 'none',
          boxShadow: 'none',
          background: 'transparent'
        }}
        autoFocus
        onPressEnter={(e) => {
          e.preventDefault();
          // 只退出编辑模式，不保存
          setEditingKey('');
        }}
        onBlur={(e) => {
          // 只退出编辑模式，不保存
          setTimeout(() => setEditingKey(''), 100);
        }}
      />;

    // 双击编辑功能
    const handleDoubleClick = () => {
      if (!editing && record) {
        edit(record);
      }
    };

    return (
      <td
        {...restProps}
        onDoubleClick={handleDoubleClick}
        style={{
          cursor: editing ? 'text' : 'pointer',
          background: editing ? '#e6f7ff' : 'transparent',
          border: editing ? '2px solid #1890ff' : '1px solid #f0f0f0',
          position: 'relative'
        }}
      >
        {editing ? (
          <Form.Item
            name={dataIndex}
            style={{ margin: 0 }}
            rules={[
              {
                required: dataIndex === 'filename',
                message: `请输入${title}!`,
              },
              // 数字字段的验证规则
              ...(isNumberField ? [{
                pattern: /^-?\d*\.?\d*$/,
                message: `${title}必须是数字!`,
              }] : [])
            ]}
            validateTrigger={['onBlur', 'onChange']}
          >
            {inputNode}
          </Form.Item>
        ) : (
          <div style={{
            padding: '2px 4px',
            minHeight: '20px',
            fontSize: '12px',
            lineHeight: '16px'
          }}>
            {children}
          </div>
        )}
      </td>
    );
  };

  // 表格列定义 - 性能测试数据表格
  const getColumns = () => {
    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 60,
        align: 'center',
        fixed: 'left',
        sorter: (a, b) => (a.id || 0) - (b.id || 0),
      },
      {
        title: '文件名',
        dataIndex: 'filename',
        key: 'filename',
        width: 150,
        editable: true,
        fixed: 'left',
        sorter: (a, b) => (a.filename || '').localeCompare(b.filename || ''),
      },
      {
        title: '成功请求数',
        dataIndex: 'success_requests',
        key: 'success_requests',
        width: 120,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.success_requests || 0) - (b.success_requests || 0),
      },
      {
        title: '基准时长(s)',
        dataIndex: 'benchmark_duration',
        key: 'benchmark_duration',
        width: 120,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.benchmark_duration || 0) - (b.benchmark_duration || 0),
      },
      {
        title: '输入Token总数',
        dataIndex: 'input_tokens',
        key: 'input_tokens',
        width: 130,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.input_tokens || 0) - (b.input_tokens || 0),
      },
      {
        title: '生成Token总数',
        dataIndex: 'output_tokens',
        key: 'output_tokens',
        width: 130,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.output_tokens || 0) - (b.output_tokens || 0),
      },
      {
        title: '请求吞吐量(req/s)',
        dataIndex: 'request_throughput',
        key: 'request_throughput',
        width: 150,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.request_throughput || 0) - (b.request_throughput || 0),
      },
      {
        title: '输出Token吞吐量(tok/s)',
        dataIndex: 'output_token_throughput',
        key: 'output_token_throughput',
        width: 180,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.output_token_throughput || 0) - (b.output_token_throughput || 0),
      },
      {
        title: '总Token吞吐量(tok/s)',
        dataIndex: 'total_token_throughput',
        key: 'total_token_throughput',
        width: 170,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.total_token_throughput || 0) - (b.total_token_throughput || 0),
      },
      {
        title: '平均TTFT(ms)',
        dataIndex: 'avg_ttft',
        key: 'avg_ttft',
        width: 130,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.avg_ttft || 0) - (b.avg_ttft || 0),
      },
      {
        title: '中位TTFT(ms)',
        dataIndex: 'median_ttft',
        key: 'median_ttft',
        width: 130,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.median_ttft || 0) - (b.median_ttft || 0),
      },
      {
        title: 'P99 TTFT(ms)',
        dataIndex: 'p99_ttft',
        key: 'p99_ttft',
        width: 130,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.p99_ttft || 0) - (b.p99_ttft || 0),
      },
      {
        title: '平均TPOT(ms)',
        dataIndex: 'avg_tpot',
        key: 'avg_tpot',
        width: 130,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.avg_tpot || 0) - (b.avg_tpot || 0),
      },
      {
        title: '中位TPOT(ms)',
        dataIndex: 'median_tpot',
        key: 'median_tpot',
        width: 130,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.median_tpot || 0) - (b.median_tpot || 0),
      },
      {
        title: 'P99 TPOT(ms)',
        dataIndex: 'p99_tpot',
        key: 'p99_tpot',
        width: 130,
        editable: true,
        align: 'center',
        sorter: (a, b) => (a.p99_tpot || 0) - (b.p99_tpot || 0),
      },
      {
        title: '操作',
        key: 'action',
        width: 120,
        fixed: 'right',
        render: (_, record) => {
          const editable = isEditing(record);
          return editable ? (
            <Space size="small">
              <Tooltip title="确认修改（不保存到服务器）">
                <Button
                  type="link"
                  size="small"
                  onClick={() => updateLocalData(record.key)}
                  style={{ padding: '0 4px', fontSize: '12px', color: '#52c41a' }}
                >
                  ✓
                </Button>
              </Tooltip>
              <Tooltip title="取消编辑">
                <Button
                  type="link"
                  size="small"
                  onClick={cancel}
                  style={{ padding: '0 4px', fontSize: '12px', color: '#ff4d4f' }}
                >
                  ✕
                </Button>
              </Tooltip>
            </Space>
          ) : (
            <Space size="small">
              <Tooltip title="双击单元格也可编辑">
                <Button
                  type="link"
                  size="small"
                  disabled={editingKey !== ''}
                  onClick={() => edit(record)}
                  style={{ padding: '0 4px', fontSize: '12px' }}
                >
                  编辑
                </Button>
              </Tooltip>
              <Button
                type="link"
                size="small"
                danger
                disabled={editingKey !== ''}
                onClick={() => deleteRecord(record)}
                style={{ padding: '0 4px', fontSize: '12px' }}
              >
                删除
              </Button>
            </Space>
          );
        },
      },
    ];

    return columns.map((col) => {
      if (!col.editable) {
        return col;
      }
      return {
        ...col,
        onCell: (record) => ({
          record,
          inputType: col.dataIndex === 'accuracy' ? 'number' : 'text',
          dataIndex: col.dataIndex,
          title: col.title,
          editing: isEditing(record),
        }),
      };
    });
  };

  // 获取性能测试数据
  const fetchModelData = useCallback(async () => {


    // 防止在编辑状态下重新获取数据 - 使用当前状态而不是依赖
    if (editingKey !== '') {

      return;
    }

    setLoading(true);
    try {
      // 先尝试从API获取数据
      try {

        // 根据模型名称获取特定模型的性能测试数据
        const response = await http.get('/api/model-storage/performance-test-data/', {
          params: { model_name: modelName }
        });


        // 转换数据格式以适配表格 - 不设置默认值为0，保持原始数据
        const transformedData = response.map((data, index) => ({
          key: data.id || `temp_${Date.now()}_${index}`, // 确保key唯一
          id: data.id || null, // 保持原始ID，新记录为null
          filename: data.filename || '',
          success_requests: data.success_requests !== null && data.success_requests !== undefined ? Number(data.success_requests) : null,
          benchmark_duration: data.benchmark_duration !== null && data.benchmark_duration !== undefined ? Number(data.benchmark_duration) : null,
          input_tokens: data.input_tokens !== null && data.input_tokens !== undefined ? Number(data.input_tokens) : null,
          output_tokens: data.output_tokens !== null && data.output_tokens !== undefined ? Number(data.output_tokens) : null,
          request_throughput: data.request_throughput !== null && data.request_throughput !== undefined ? Number(data.request_throughput) : null,
          output_token_throughput: data.output_token_throughput !== null && data.output_token_throughput !== undefined ? Number(data.output_token_throughput) : null,
          total_token_throughput: data.total_token_throughput !== null && data.total_token_throughput !== undefined ? Number(data.total_token_throughput) : null,
          avg_ttft: data.avg_ttft !== null && data.avg_ttft !== undefined ? Number(data.avg_ttft) : null,
          median_ttft: data.median_ttft !== null && data.median_ttft !== undefined ? Number(data.median_ttft) : null,
          p99_ttft: data.p99_ttft !== null && data.p99_ttft !== undefined ? Number(data.p99_ttft) : null,
          avg_tpot: data.avg_tpot !== null && data.avg_tpot !== undefined ? Number(data.avg_tpot) : null,
          median_tpot: data.median_tpot !== null && data.median_tpot !== undefined ? Number(data.median_tpot) : null,
          p99_tpot: data.p99_tpot !== null && data.p99_tpot !== undefined ? Number(data.p99_tpot) : null,
          // 保存原始数据用于更新
          _original: data
        }));

        setModelData(transformedData);
      } catch (apiError) {
        // 如果API失败，使用示例数据
        const sampleData = [
          {
            key: 1,
            id: 1,
            filename: 'test_model_v1.json',
            success_requests: 1000,
            benchmark_duration: 60.5,
            input_tokens: 50000,
            output_tokens: 25000,
            request_throughput: 16.53,
            output_token_throughput: 413.22,
            total_token_throughput: 1239.67,
            avg_ttft: 245.6,
            median_ttft: 230.1,
            p99_ttft: 450.8,
            avg_tpot: 12.3,
            median_tpot: 11.8,
            p99_tpot: 18.9,
            _original: { id: 1 }
          },
          {
            key: 2,
            id: 2,
            filename: 'test_model_v2.json',
            success_requests: 800,
            benchmark_duration: 45.2,
            input_tokens: 40000,
            output_tokens: 20000,
            request_throughput: 17.70,
            output_token_throughput: 442.48,
            total_token_throughput: 1327.43,
            avg_ttft: 220.3,
            median_ttft: 210.5,
            p99_ttft: 380.2,
            avg_tpot: 11.5,
            median_tpot: 11.2,
            p99_tpot: 16.8,
            _original: { id: 2 }
          },
          {
            key: 3,
            id: 3,
            filename: 'benchmark_test.json',
            success_requests: 1200,
            benchmark_duration: 75.8,
            input_tokens: 60000,
            output_tokens: 30000,
            request_throughput: 15.83,
            output_token_throughput: 395.78,
            total_token_throughput: 1187.35,
            avg_ttft: 280.1,
            median_ttft: 265.7,
            p99_ttft: 520.4,
            avg_tpot: 13.8,
            median_tpot: 13.2,
            p99_tpot: 21.5,
            _original: { id: 3 }
          }
        ];
        setModelData(sampleData);
      }
    } catch (error) {
      message.error('获取数据失败');

    } finally {
      setLoading(false);
    }
  }, [modelName]); // 移除editingKey依赖，避免无限循环



  useEffect(() => {
    if (modelName) {
      fetchModelData();
    }
  }, [modelName]); // 移除fetchModelData依赖，避免无限循环

  // 原生Excel粘贴体验
  useEffect(() => {
    const handlePaste = async (e) => {
      // 只在表格区域内处理粘贴
      if (!e.target.closest('.ant-table-wrapper')) {
        return;
      }

      e.preventDefault();

      try {
        const clipboardData = e.clipboardData || window.clipboardData;
        const pastedText = clipboardData.getData('text');

        if (!pastedText.trim()) {
          return;
        }

        // 解析Excel数据 - 更宽松的解析
        const lines = pastedText.trim().split('\n');
        const newRows = [];

        lines.forEach((line, index) => {
          const columns = line.split('\t');

          // 只要有文件名就创建行
          if (columns.length > 0 && columns[0]?.trim()) {
            const record = {
              key: Date.now() + index,
              id: null, // 新记录没有数据库ID
              filename: columns[0]?.trim() || `file_${index + 1}`,
              success_requests: parseInt(columns[1]) || 0,
              benchmark_duration: parseFloat(columns[2]) || 0,
              input_tokens: parseInt(columns[3]) || 0,
              output_tokens: parseInt(columns[4]) || 0,
              request_throughput: parseFloat(columns[5]) || 0,
              output_token_throughput: parseFloat(columns[6]) || 0,
              total_token_throughput: parseFloat(columns[7]) || 0,
              avg_ttft: parseFloat(columns[8]) || 0,
              median_ttft: parseFloat(columns[9]) || 0,
              p99_ttft: parseFloat(columns[10]) || 0,
              avg_tpot: parseFloat(columns[11]) || 0,
              median_tpot: parseFloat(columns[12]) || 0,
              p99_tpot: parseFloat(columns[13]) || 0
            };
            newRows.push(record);
          }
        });

        if (newRows.length > 0) {
          setModelData([...modelData, ...newRows]);
          message.success(`✅ 成功粘贴 ${newRows.length} 行数据`);
        } else {
          message.warning('未检测到有效数据');
        }

      } catch (error) {
        message.error('粘贴失败');

      }
    };

    const handleKeyDown = (e) => {
      // Ctrl+N 添加空行
      if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        addEmptyRow();
      }
      // Ctrl+S 保存全部
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        saveAllData();
      }
      // ESC 取消编辑
      if (e.key === 'Escape' && editingKey) {
        cancel();
      }
    };

    // 添加全局粘贴监听
    document.addEventListener('paste', handlePaste);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('paste', handlePaste);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [modelData]);

  return (
    <div style={{ padding: 24 }}>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <EditOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span>🚀 React 17 优化版Excel表格 - {decodeURIComponent(modelName)}</span>
          </div>
        }
        extra={
          <Space>
            <div style={{ color: '#666', fontSize: '12px', marginRight: 16 }}>
              🚀 Excel体验 | 点击列头排序 | Ctrl+V粘贴 | Ctrl+N添加行 | Ctrl+S批量保存 | 双击编辑
            </div>

            {/* 修改状态提示 */}
            {(() => {
              const modifiedCount = modelData.filter(item => item._modified).length;
              if (modifiedCount > 0) {
                return (
                  <div style={{
                    background: '#fff7e6',
                    border: '1px solid #faad14',
                    borderRadius: '4px',
                    padding: '4px 8px',
                    fontSize: '12px',
                    color: '#d46b08'
                  }}>
                    📝 {modifiedCount} 条数据已修改，请点击"批量保存"保存到服务器
                  </div>
                );
              }
              return null;
            })()}

            <Button
              icon={<ReloadOutlined />}
              onClick={fetchModelData}
              disabled={loading}
            >
              刷新
            </Button>

            <Tooltip title="只保存修改过的数据到服务器">
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={saveAllData}
                disabled={loading || modelData.filter(item => item._modified).length === 0}
                danger={modelData.filter(item => item._modified).length > 0}
              >
                批量保存 {modelData.filter(item => item._modified).length > 0 && `(${modelData.filter(item => item._modified).length})`}
              </Button>
            </Tooltip>

            <Tooltip title="添加一行空数据，可直接编辑">
              <Button
                icon={<PlusOutlined />}
                onClick={addEmptyRow}
              >
                添加空行
              </Button>
            </Tooltip>
          </Space>
        }
      >
        {/* 原生Excel粘贴提示 */}
        <div style={{
          marginBottom: 16,
          padding: '12px 16px',
          background: 'linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%)',
          border: '1px solid #91d5ff',
          borderRadius: '8px',
          fontSize: '13px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
            <span style={{ fontSize: '16px', marginRight: 8 }}>📋</span>
            <strong style={{ color: '#1890ff' }}>原生Excel粘贴体验</strong>
          </div>
          <div style={{ color: '#666' }}>
            1. <kbd style={{padding: '2px 6px', background: '#f5f5f5', border: '1px solid #d9d9d9', borderRadius: '3px'}}>点击列头</kbd> 排序数据（支持多列排序） |
            <kbd style={{padding: '2px 6px', background: '#f5f5f5', border: '1px solid #d9d9d9', borderRadius: '3px'}}>Ctrl+V</kbd> 粘贴Excel数据 |
            <kbd style={{padding: '2px 6px', background: '#f5f5f5', border: '1px solid #d9d9d9', borderRadius: '3px'}}>Ctrl+N</kbd> 添加空行
            <br />
            2. <kbd style={{padding: '2px 6px', background: '#f5f5f5', border: '1px solid #d9d9d9', borderRadius: '3px'}}>Ctrl+S</kbd> 保存全部 |
            <kbd style={{padding: '2px 6px', background: '#f5f5f5', border: '1px solid #d9d9d9', borderRadius: '3px'}}>ESC</kbd> 取消编辑 |
            <kbd style={{padding: '2px 6px', background: '#f5f5f5', border: '1px solid #d9d9d9', borderRadius: '3px'}}>双击单元格</kbd> 编辑数据
          </div>
        </div>

        <Form form={editForm} component={false}>
          <Table
            components={{
              body: {
                cell: EditableCell,
              },
            }}
            bordered
            dataSource={modelData}
            columns={getColumns()}
            rowClassName={(record) => {
              let className = "editable-row";
              if (record._modified) {
                className += " modified-row";
              }
              if (isEditing(record)) {
                className += " editing-row";
              }
              return className;
            }}
            size="small"
            pagination={{
              onChange: cancel,
              pageSize: 50,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              pageSizeOptions: ['20', '50', '100', '200'],
            }}
            loading={loading}
            scroll={{ x: 2000, y: 500 }}
            style={{
              fontSize: '12px'
            }}
            onChange={(pagination, filters, sorter, extra) => {
              // 处理表格变化事件，包括排序

              // 如果正在编辑，取消编辑状态
              if (editingKey) {
                cancel();
              }
            }}
          />
        </Form>
      </Card>


    </div>
  );
}

export default ModelDataTableSimple;
