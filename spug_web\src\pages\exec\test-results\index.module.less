/* 测试结果页面 - 现代化设计 */
.container {
  min-height: 100vh;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;

  /* 动态背景效果 */
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundMove 20s ease-in-out infinite;
    z-index: -1;
    pointer-events: none;
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

@keyframes backgroundMove {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(-20px) translateY(-10px);
  }
  50% {
    transform: translateX(20px) translateY(10px);
  }
  75% {
    transform: translateX(-10px) translateY(20px);
  }
}

/* 页面标题 */
.pageHeader {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 24px;
  padding: 24px 32px;

  .headerTitle {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;

    .headerIcon {
      font-size: 32px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .headerSubtitle {
    color: #666;
    font-size: 16px;
    margin-top: 8px;
    font-weight: 400;
  }
}

/* 统计卡片行 */
.statsRow {
  margin-bottom: 24px;

  .statsCard {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
    }

    :global(.ant-card-body) {
      padding: 24px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      }
    }

    :global(.ant-statistic-title) {
      color: #666;
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    :global(.ant-statistic-content) {
      font-size: 32px;
      font-weight: 700;
      color: #2c3e50;

      :global(.ant-statistic-content-value) {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    :global(.anticon) {
      font-size: 24px;
      color: #667eea;
      margin-right: 8px;
    }
  }
}

/* 筛选卡片 */
.filterCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 24px;

  :global(.ant-card-body) {
    padding: 24px;
  }

  :global(.ant-card-head) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

    :global(.ant-card-head-title) {
      font-weight: 600;
      color: #2c3e50;
    }
  }
}

/* 工具栏卡片 */
.toolbarCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 24px;

  :global(.ant-card-body) {
    padding: 20px 24px;
  }
}

/* 主要内容卡片 */
.mainCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);

  :global(.ant-card-body) {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
  }

  :global(.ant-table) {
    background: transparent;

    :global(.ant-table-container) {
      border-radius: 20px;
    }

    :global(.ant-table-thead > tr > th) {
      background: rgba(102, 126, 234, 0.1);
      border-bottom: 2px solid rgba(102, 126, 234, 0.2);
      font-weight: 600;
      color: #2c3e50;
      padding: 16px;
      font-size: 14px;

      &:first-child {
        border-top-left-radius: 20px;
      }

      &:last-child {
        border-top-right-radius: 20px;
      }
    }

    :global(.ant-table-tbody > tr > td) {
      padding: 16px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
    }

    :global(.ant-table-tbody > tr:hover > td) {
      background: rgba(102, 126, 234, 0.05);
    }

    :global(.ant-table-tbody > tr:last-child > td) {
      border-bottom: none;

      &:first-child {
        border-bottom-left-radius: 20px;
      }

      &:last-child {
        border-bottom-right-radius: 20px;
      }
    }
  }
}

/* 按钮样式 */
.primaryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  height: 40px;
  padding: 0 20px;

  &:hover, &:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    background: #d1d5db;
    box-shadow: none;
    transform: none;
    cursor: not-allowed;
  }
}

.secondaryButton {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 12px;
  color: #667eea;
  font-weight: 600;
  height: 40px;
  padding: 0 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover, &:focus {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
}

/* 计划名称样式 */
.planName {
  font-weight: 600;
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: #5a6fd8;
    text-decoration: underline;
  }
}

/* 指标统计样式 */
.metricsStats {
  font-size: 12px;
  line-height: 1.6;
  color: #6b7280;

  .totalCount {
    color: #667eea;
    font-weight: 600;
    background: rgba(102, 126, 234, 0.1);
    padding: 2px 6px;
    border-radius: 6px;
    margin: 0 2px;
  }

  .confirmedCount {
    color: #10b981;
    font-weight: 600;
    background: rgba(16, 185, 129, 0.1);
    padding: 2px 6px;
    border-radius: 6px;
    margin: 0 2px;
  }
}

/* 状态标签样式 */
.statusTag {
  border-radius: 12px;
  font-weight: 600;
  font-size: 12px;
  padding: 4px 12px;
  border: none;

  &.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
  }

  &.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
  }

  &.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
  }

  &.info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
  }
}

/* 输入框样式 */
.searchInput {
  border-radius: 12px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(102, 126, 234, 0.4);
  }

  &:focus, &.ant-input-focused {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

/* 选择器样式 */
.customSelect {
  :global(.ant-select-selector) {
    border-radius: 12px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(102, 126, 234, 0.4);
    }
  }

  &:global(.ant-select-focused) {
    :global(.ant-select-selector) {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
}

/* 日期选择器样式 */
.customDatePicker {
  border-radius: 12px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(102, 126, 234, 0.4);
  }

  &:focus, &.ant-picker-focused {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }

  .pageHeader {
    padding: 20px 24px;

    .headerTitle {
      font-size: 24px;

      .headerIcon {
        font-size: 28px;
      }
    }

    .headerSubtitle {
      font-size: 14px;
    }
  }

  .statsRow {
    margin-bottom: 20px;

    .statsCard {
      :global(.ant-card-body) {
        padding: 20px;
      }

      :global(.ant-statistic-content) {
        font-size: 28px;
      }
    }
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .pageHeader {
    padding: 16px 20px;
    margin-bottom: 16px;

    .headerTitle {
      font-size: 20px;

      .headerIcon {
        font-size: 24px;
      }
    }

    .headerSubtitle {
      font-size: 13px;
    }
  }

  .statsRow {
    margin-bottom: 16px;

    .statsCard {
      :global(.ant-card-body) {
        padding: 16px;
      }

      :global(.ant-statistic-title) {
        font-size: 12px;
      }

      :global(.ant-statistic-content) {
        font-size: 24px;
      }
    }
  }

  .filterCard, .toolbarCard, .mainCard {
    margin-bottom: 16px;
    border-radius: 16px;

    :global(.ant-card-body) {
      padding: 16px;
    }
  }

  .primaryButton, .secondaryButton {
    height: 36px;
    padding: 0 16px;
    font-size: 13px;
  }
}