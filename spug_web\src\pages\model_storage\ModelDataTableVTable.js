import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Button, message, Space, Modal, Tooltip } from 'antd';
import { 
  SaveOutlined, PlusOutlined, DeleteOutlined, ReloadOutlined, 
  FileExcelOutlined, UndoOutlined, RedoOutlined, ExportOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import http from 'libs/http';

function ModelDataTableVTable() {
  const { modelName } = useParams();
  
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // 引用
  const tableRef = useRef(null);
  const tableInstanceRef = useRef(null);
  
  // 获取数据
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/model-storage/performance-test-data/', {
        params: { model_name: modelName }
      });

      const transformedData = response.map(item => ({
        ...item,
        id: item.id || `temp_${Date.now()}_${Math.random()}`
      }));

      setData(transformedData);
      setHasUnsavedChanges(false);

    } catch (error) {
      message.error('获取数据失败');
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [modelName]);

  // 数据验证函数
  const validateRowData = (row, index) => {
    const errors = [];

    // 必填字段验证
    if (!row.filename || !row.filename.trim()) {
      errors.push(`第${index + 1}行：文件名不能为空`);
    }

    // 数值范围验证
    const numericFields = {
      success_requests: { name: '成功请求数', min: 0, max: 1000000 },
      benchmark_duration: { name: '基准时长', min: 0, max: 86400 },
      input_tokens: { name: '输入Token总数', min: 0, max: 10000000 },
      output_tokens: { name: '生成Token总数', min: 0, max: 10000000 },
      request_throughput: { name: '请求吞吐量', min: 0, max: 10000 },
      output_token_throughput: { name: '输出Token吞吐量', min: 0, max: 100000 },
      total_token_throughput: { name: '总Token吞吐量', min: 0, max: 100000 },
      avg_ttft: { name: '平均TTFT', min: 0, max: 60000 },
      median_ttft: { name: '中位TTFT', min: 0, max: 60000 },
      p99_ttft: { name: 'P99 TTFT', min: 0, max: 60000 },
      avg_tpot: { name: '平均TPOT', min: 0, max: 10000 },
      median_tpot: { name: '中位TPOT', min: 0, max: 10000 },
      p99_tpot: { name: 'P99 TPOT', min: 0, max: 10000 }
    };

    Object.entries(numericFields).forEach(([field, config]) => {
      const value = parseFloat(row[field]) || 0;
      if (value < config.min || value > config.max) {
        errors.push(`第${index + 1}行：${config.name}必须在${config.min}到${config.max}之间`);
      }
    });

    // 文件名格式验证
    if (row.filename) {
      const filename = row.filename.trim();
      if (filename.length > 256) {
        errors.push(`第${index + 1}行：文件名长度不能超过256个字符`);
      }
      if (!/^[a-zA-Z0-9._-]+$/.test(filename)) {
        errors.push(`第${index + 1}行：文件名只能包含字母、数字、点、下划线和连字符`);
      }
    }

    return errors;
  };

  // 保存数据
  const saveData = useCallback(async () => {
    if (!tableInstanceRef.current) {
      message.error('表格未初始化');
      return;
    }

    setLoading(true);
    try {
      const tableData = tableInstanceRef.current.records;

      // 数据验证
      const allErrors = [];
      tableData.forEach((row, index) => {
        const errors = validateRowData(row, index);
        allErrors.push(...errors);
      });

      if (allErrors.length > 0) {
        message.error(`数据验证失败：${allErrors.slice(0, 3).join('；')}${allErrors.length > 3 ? '等' : ''}`);
        return;
      }

      // 分类处理数据：新增、更新、删除
      const dataToCreate = [];
      const dataToUpdate = [];
      const existingIds = new Set();

      tableData.forEach(row => {
        const saveRowData = {
          model_name: modelName,
          filename: (row.filename || '').trim(),
          success_requests: Math.max(0, parseInt(row.success_requests) || 0),
          benchmark_duration: Math.max(0, parseFloat(row.benchmark_duration) || 0),
          input_tokens: Math.max(0, parseInt(row.input_tokens) || 0),
          output_tokens: Math.max(0, parseInt(row.output_tokens) || 0),
          request_throughput: Math.max(0, parseFloat(row.request_throughput) || 0),
          output_token_throughput: Math.max(0, parseFloat(row.output_token_throughput) || 0),
          total_token_throughput: Math.max(0, parseFloat(row.total_token_throughput) || 0),
          avg_ttft: Math.max(0, parseFloat(row.avg_ttft) || 0),
          median_ttft: Math.max(0, parseFloat(row.median_ttft) || 0),
          p99_ttft: Math.max(0, parseFloat(row.p99_ttft) || 0),
          avg_tpot: Math.max(0, parseFloat(row.avg_tpot) || 0),
          median_tpot: Math.max(0, parseFloat(row.median_tpot) || 0),
          p99_tpot: Math.max(0, parseFloat(row.p99_tpot) || 0)
        };

        if (row.id && !row.id.toString().startsWith('temp_')) {
          // 现有数据，需要更新
          saveRowData.id = row.id;
          dataToUpdate.push(saveRowData);
          existingIds.add(row.id);
        } else {
          // 新数据，需要创建
          dataToCreate.push(saveRowData);
        }
      });

      let successCount = 0;
      let errorCount = 0;
      const detailedErrors = [];

      // 批量创建新数据
      if (dataToCreate.length > 0) {
        try {
          const createResponse = await http.post('/api/model-storage/performance-test-data/batch/', {
            data: dataToCreate
          });

          if (createResponse && createResponse.created) {
            // 更新表格中的临时ID为真实ID
            createResponse.created.forEach((newRecord, index) => {
              if (newRecord.id) {
                const originalRow = tableData.find(row =>
                  row.filename === newRecord.filename &&
                  row.id && row.id.toString().startsWith('temp_')
                );
                if (originalRow) {
                  originalRow.id = newRecord.id;
                }
              }
            });
            successCount += createResponse.created.length;

            // 处理批量创建中的错误
            if (createResponse.errors && createResponse.errors.length > 0) {
              detailedErrors.push(...createResponse.errors);
              errorCount += createResponse.errors.length;
            }
          } else if (createResponse && Array.isArray(createResponse)) {
            // 兼容旧格式
            createResponse.forEach((newRecord, index) => {
              if (newRecord.id) {
                const originalRow = tableData.find(row =>
                  row.id && row.id.toString().startsWith('temp_')
                );
                if (originalRow) {
                  originalRow.id = newRecord.id;
                }
              }
            });
            successCount += createResponse.length;
          }

        } catch (createError) {
          console.error('批量创建失败:', createError);

          // 如果批量创建失败，尝试逐个创建
          for (let i = 0; i < dataToCreate.length; i++) {
            const rowData = dataToCreate[i];
            try {
              const response = await http.post('/api/model-storage/performance-test-data/', rowData);
              if (response.id) {
                // 更新表格中对应行的ID
                const originalRow = tableData.find(row =>
                  row.filename === rowData.filename &&
                  row.id && row.id.toString().startsWith('temp_')
                );
                if (originalRow) {
                  originalRow.id = response.id;
                }
              }
              successCount++;
            } catch (error) {
              errorCount++;
              detailedErrors.push(`创建第${i + 1}条数据失败: ${error.message || '未知错误'}`);
            }
          }
        }
      }

      // 批量更新现有数据
      if (dataToUpdate.length > 0) {
        try {
          const updateResponse = await http.put('/api/model-storage/performance-test-data/batch/', {
            data: dataToUpdate
          });

          if (updateResponse && updateResponse.updated) {
            successCount += updateResponse.updated.length;

            // 处理批量更新中的错误
            if (updateResponse.errors && updateResponse.errors.length > 0) {
              detailedErrors.push(...updateResponse.errors);
              errorCount += updateResponse.errors.length;
            }
          } else {
            successCount += dataToUpdate.length;
          }

        } catch (updateError) {
          console.error('批量更新失败:', updateError);

          // 如果批量更新失败，尝试逐个更新
          for (let i = 0; i < dataToUpdate.length; i++) {
            const rowData = dataToUpdate[i];
            try {
              await http.put(`/api/model-storage/performance-test-data/${rowData.id}/`, rowData);
              successCount++;
            } catch (error) {
              errorCount++;
              detailedErrors.push(`更新第${i + 1}条数据失败: ${error.message || '未知错误'}`);
            }
          }
        }
      }

      // 显示详细的操作结果
      if (errorCount === 0) {
        message.success(`保存成功！共处理 ${successCount} 条记录`);
        setHasUnsavedChanges(false);
        fetchData(); // 重新获取数据以确保同步
      } else if (successCount > 0) {
        const errorSummary = detailedErrors.slice(0, 3).join('；') + (detailedErrors.length > 3 ? '等' : '');
        message.warning(`部分保存成功：成功 ${successCount} 条，失败 ${errorCount} 条。错误详情：${errorSummary}`);
        // 即使有错误也重新获取数据，确保显示最新状态
        fetchData();
      } else {
        const errorSummary = detailedErrors.slice(0, 5).join('；') + (detailedErrors.length > 5 ? '等' : '');
        message.error(`保存失败：${errorSummary}`);
      }

    } catch (error) {
      console.error('保存操作异常:', error);
      message.error(`保存失败：${error.message || '网络错误，请检查网络连接'}`);
    } finally {
      setLoading(false);
    }
  }, [modelName, fetchData]);

  // 插入行
  const insertRow = useCallback(() => {
    if (tableInstanceRef.current) {
      const newRow = {};
      getColumnsConfig().forEach(col => {
        newRow[col.field] = '';
      });

      // 获取当前所有数据中最大的数字ID，然后+1
      const currentData = tableInstanceRef.current.getAllRowsData();
      const maxId = Math.max(
        ...currentData
          .map(row => parseInt(row.id))
          .filter(id => !isNaN(id)),
        0
      );
      newRow.id = maxId + 1;

      tableInstanceRef.current.addRecord(newRow);
      setHasUnsavedChanges(true);
      message.success('已添加新行');
    }
  }, []);

  // 导出数据
  const exportData = useCallback(() => {
    if (!tableInstanceRef.current) {
      message.error('表格未初始化');
      return;
    }

    try {
      const tableData = tableInstanceRef.current.records;
      const columns = getColumnsConfig();
      
      // 构造 CSV 数据
      const headers = columns.map(col => col.title).join(',');
      const rows = tableData.map(row => 
        columns.map(col => row[col.field] || '').join(',')
      );
      const csvContent = [headers, ...rows].join('\n');
      
      // 下载文件
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `${modelName}_data_${new Date().toISOString().slice(0, 10)}.csv`;
      link.click();
      
      message.success('数据已导出');
    } catch (error) {
      message.error('导出失败');
    }
  }, [modelName]);

  // VTable 列配置
  const getColumnsConfig = (hasEditor = true) => [
    {
      field: 'id',
      title: 'ID',
      width: 80,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'filename',
      title: '文件名',
      width: 150,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'success_requests',
      title: '成功请求数',
      width: 120,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'benchmark_duration',
      title: '基准时长(s)',
      width: 120,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'input_tokens',
      title: '输入Token总数',
      width: 130,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'output_tokens',
      title: '生成Token总数',
      width: 130,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'request_throughput',
      title: '请求吞吐量(req/s)',
      width: 150,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'output_token_throughput',
      title: '输出Token吞吐量(tok/s)',
      width: 180,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'total_token_throughput',
      title: '总Token吞吐量(tok/s)',
      width: 170,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'avg_ttft',
      title: '平均TTFT(ms)',
      width: 130,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'median_ttft',
      title: '中位TTFT(ms)',
      width: 130,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'p99_ttft',
      title: 'P99 TTFT(ms)',
      width: 130,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'avg_tpot',
      title: '平均TPOT(ms)',
      width: 130,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'median_tpot',
      title: '中位TPOT(ms)',
      width: 130,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    },
    {
      field: 'p99_tpot',
      title: 'P99 TPOT(ms)',
      width: 130,
      sort: true,
      ...(hasEditor && { editor: 'input-editor' })
    }
  ];

  // 初始化 VTable
  const initVTable = useCallback(async () => {
    if (!tableRef.current || !window.VTable) {
      return;
    }

    // 确保 process 对象存在
    if (typeof window.process === 'undefined') {
      window.process = {
        env: { NODE_ENV: 'development' },
        browser: true
      };
    }

    try {
      // 清理之前的实例
      if (tableInstanceRef.current) {
        try {
          tableInstanceRef.current.release();
        } catch (e) {
          // 忽略清理错误
        }
        tableInstanceRef.current = null;
      }

      // 注册编辑器
      let inputEditor;
      let hasEditor = false;

      if (window.VTable && window.VTable.editors && window.VTable.editors.InputEditor) {
        try {
          inputEditor = new window.VTable.editors.InputEditor();
          window.VTable.register.editor('input-editor', inputEditor);
          hasEditor = true;
        } catch (e) {
          // 编辑器注册失败
        }
      } else if (window.VTable_editors && window.VTable_editors.InputEditor) {
        try {
          inputEditor = new window.VTable_editors.InputEditor();
          window.VTable.register.editor('input-editor', inputEditor);
          hasEditor = true;
        } catch (e) {
          // 编辑器注册失败
        }
      }

      if (!hasEditor) {
        message.warning('编辑器未加载，表格将以只读模式运行');
      }

      // VTable 配置 - 如果没有数据，创建少量空行供编辑
      let tableData = data;
      if (data.length === 0) {
        // 只创建2行空数据供编辑使用
        tableData = Array.from({ length: 2 }, (_, index) => ({
          id: `temp_${index + 1}`,
          filename: '',
          success_requests: '',
          benchmark_duration: '',
          input_tokens: '',
          output_tokens: '',
          request_throughput: '',
          output_token_throughput: '',
          total_token_throughput: '',
          avg_ttft: '',
          median_ttft: '',
          p99_ttft: '',
          avg_tpot: '',
          median_tpot: '',
          p99_tpot: ''
        }));
      }

      const option = {
        records: tableData,
        columns: getColumnsConfig(hasEditor),
        widthMode: 'standard',
        frozenColCount: 1,
        defaultRowHeight: 36,
        autoWrapText: true,
        limitMaxAutoWidth: 600,
        heightMode: 'autoHeight',
        editCellTrigger: 'doubleclick',
        keyboardOptions: {
          moveFocusCellOnTab: true,
          editCellOnEnter: true,
          moveEditCellOnArrowKeys: true,
          selectAllOnCtrlA: true,
          copySelected: true,
          pasteValueToCell: true
        },
        menu: {
          contextMenuItems: [
            'copy',
            'paste',
            'separator',
            {
              menuKey: 'delete',
              text: '清空单元格',
              icon: 'delete'
            }
          ]
        },
        theme: window.VTable.themes.DEFAULT.extends({
          defaultStyle: {
            textAlign: 'left',
            padding: [4, 8, 4, 8],
            fontSize: 14
          },
          headerStyle: {
            textAlign: 'center',
            fontWeight: 'bold',
            bgColor: '#f5f5f5'
          }
        })
      };

      // 创建表格实例
      const tableInstance = new window.VTable.ListTable(tableRef.current, option);
      tableInstanceRef.current = tableInstance;

      // 监听编辑事件
      tableInstance.on('change_cell_value', (args) => {
        setHasUnsavedChanges(true);
      });

      // 添加键盘事件监听
      tableInstance.on('keydown', (args) => {
        const { event, col, row } = args;

        if (event.key === 'Delete' || event.key === 'Backspace') {
          event.preventDefault();

          try {
            let deletedCount = 0;

            try {
              const selectedCells = tableInstance.getSelectedCellInfos();

              if (selectedCells && selectedCells.length > 0) {
                selectedCells.forEach(cellInfo => {
                  if (Array.isArray(cellInfo) && cellInfo.length >= 2) {
                    const cellCol = cellInfo[0];
                    const cellRow = cellInfo[1];

                    if (cellCol !== undefined && cellRow !== undefined) {
                      tableInstance.changeCellValue(cellCol, cellRow, '');
                      deletedCount++;
                    }
                  } else if (cellInfo && typeof cellInfo === 'object') {
                    const cellCol = cellInfo.col;
                    const cellRow = cellInfo.row;

                    if (cellCol !== undefined && cellRow !== undefined) {
                      tableInstance.changeCellValue(cellCol, cellRow, '');
                      deletedCount++;
                    }
                  }
                });
              }
            } catch (selectionError) {
              // 获取选中单元格失败，尝试其他方法
            }

            // 备用方案
            if (deletedCount === 0 && col !== undefined && row !== undefined) {
              const records = tableInstance.records;
              const columns = getColumnsConfig();

              if (row >= 0 && row < records.length && col >= 0 && col < columns.length) {
                tableInstance.changeCellValue(col, row, '');
                deletedCount = 1;
              }
            }

            if (deletedCount > 0) {
              setHasUnsavedChanges(true);
              message.success(`已清空 ${deletedCount} 个单元格的内容`);
            } else {
              message.info('没有找到可清空的单元格，请先点击选中单元格');
            }

          } catch (error) {
            message.error('删除失败: ' + error.message);
          }
        }
      });

      // 添加右键菜单事件监听
      tableInstance.on('dropdown_menu_click', (args) => {
        const { menuKey, col, row } = args;

        switch (menuKey) {
          case 'delete':
            tableInstance.changeCellValue(col, row, '');
            setHasUnsavedChanges(true);
            message.success('已清空单元格内容');
            break;
          default:
            break;
        }
      });

      message.success('VTable 表格初始化成功！双击单元格即可编辑');

    } catch (error) {
      message.error(`表格初始化失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  }, [data]);

  // 初始化数据
  useEffect(() => {
    if (modelName) {
      fetchData();
    }
  }, [modelName, fetchData]);

  // 初始化表格
  useEffect(() => {
    if (window.VTable) {
      initVTable();
    }
  }, [data, initVTable]);

  return (
    <div style={{ padding: '20px' }}>
      <Card
        title={`${modelName} - 性能测试数据 (VTable版)`}
        style={{ position: 'relative' }}
      >
        {/* 工具栏 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveData}
              loading={loading}
              disabled={!hasUnsavedChanges}
            >
              保存数据
            </Button>

            <Button
              icon={<PlusOutlined />}
              onClick={insertRow}
            >
              新增行
            </Button>

            <Button
              icon={<ReloadOutlined />}
              onClick={fetchData}
              loading={loading}
            >
              刷新数据
            </Button>

            <Button
              icon={<ExportOutlined />}
              onClick={exportData}
            >
              导出CSV
            </Button>

            <Button
              danger
              onClick={() => {
                if (tableInstanceRef.current) {
                  try {
                    const records = tableInstanceRef.current.records;
                    const columns = getColumnsConfig();

                    // 找出空行（所有字段都为空的行）
                    const nonEmptyRows = records.filter(row => {
                      return columns.some(col => {
                        const value = row[col.field];
                        return value !== null && value !== undefined && value !== '';
                      });
                    });

                    const deletedCount = records.length - nonEmptyRows.length;
                    if (deletedCount > 0) {
                      tableInstanceRef.current.setRecords(nonEmptyRows);
                      setHasUnsavedChanges(true);
                      message.success(`已删除 ${deletedCount} 个空行`);
                    } else {
                      message.info('没有找到空行');
                    }
                  } catch (error) {
                    message.error('删除空行失败: ' + error.message);
                  }
                } else {
                  message.error('表格未初始化');
                }
              }}
            >
              删除空行
            </Button>
          </Space>

          {hasUnsavedChanges && (
            <span style={{ marginLeft: '16px', color: '#ff4d4f' }}>
              ⚠️ 有未保存的更改
            </span>
          )}
        </div>

        {/* VTable 容器 */}
        <div
          ref={tableRef}
          style={{
            width: '100%',
            height: '600px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px'
          }}
        />

        {loading && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: 'rgba(255,255,255,0.8)',
            padding: '20px',
            borderRadius: '6px'
          }}>
            加载中...
          </div>
        )}
      </Card>
    </div>
  );
}

export default ModelDataTableVTable;
