import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Button, message, Space, Modal, Tooltip, Upload } from 'antd';
import {
  ReloadOutlined, FileExcelOutlined, ExportOutlined, ImportOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import http from 'libs/http';

function ModelDataTableVTable() {
  const { modelName } = useParams();
  
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);
  
  // 引用
  const tableRef = useRef(null);
  const tableInstanceRef = useRef(null);
  
  // 获取数据
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/model-storage/performance-test-data/', {
        params: { model_name: modelName }
      });

      const transformedData = response.map(item => ({
        ...item,
        id: item.id || `temp_${Date.now()}_${Math.random()}`
      }));

      setData(transformedData);

    } catch (error) {
      message.error('获取数据失败');
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [modelName]);

  // 导出数据 - 使用VTable官方方法
  const insertRow = useCallback(() => {
    if (tableInstanceRef.current) {
      const newRow = {};
      getColumnsConfig().forEach(col => {
        newRow[col.field] = '';
      });

      // 获取当前所有数据中最大的数字ID，然后+1
      const currentData = tableInstanceRef.current.getAllRowsData();
      const maxId = Math.max(
        ...currentData
          .map(row => parseInt(row.id))
          .filter(id => !isNaN(id)),
        0
      );
      newRow.id = maxId + 1;

      tableInstanceRef.current.addRecord(newRow);
      setHasUnsavedChanges(true);
      message.success('已添加新行');
    }
  }, []);

  // 导出数据 - 使用VTable官方方法
  const exportData = useCallback(() => {
    if (!tableInstanceRef.current) {
      message.error('表格未初始化');
      return;
    }

    try {
      // 使用VTable官方的导出功能
      if (window.VTable && window.VTable.exportCsv) {
        window.VTable.exportCsv(tableInstanceRef.current, {
          filename: `${modelName}_performance_data_${new Date().toISOString().slice(0, 10)}.csv`
        });
        message.success('数据已导出');
      } else {
        // 备用方案：手动构造CSV
        const tableData = tableInstanceRef.current.records;
        const columns = getColumnsConfig();

        const headers = columns.map(col => col.title).join(',');
        const rows = tableData.map(row =>
          columns.map(col => {
            const value = row[col.field];
            return value !== null && value !== undefined ? value : '';
          }).join(',')
        );
        const csvContent = [headers, ...rows].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `${modelName}_performance_data_${new Date().toISOString().slice(0, 10)}.csv`;
        link.click();

        message.success('数据已导出');
      }
    } catch (error) {
      message.error('导出失败: ' + error.message);
    }
  }, [modelName]);

  // 导入CSV数据
  const importCSV = useCallback((file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csv = e.target.result;
        const lines = csv.split('\n').filter(line => line.trim());

        if (lines.length < 2) {
          message.error('CSV文件格式不正确，至少需要标题行和一行数据');
          return;
        }

        // 解析CSV数据
        const headers = lines[0].split(',').map(h => h.trim());
        const rows = lines.slice(1).map(line => {
          const values = line.split(',').map(v => v.trim());
          const row = {};
          headers.forEach((header, index) => {
            const value = values[index] || '';
            // 根据列名映射到对应字段
            switch (header) {
              case 'ID':
                row.id = value;
                break;
              case '文件名':
                row.filename = value;
                break;
              case '成功请求数':
                row.success_requests = value === '' ? null : Number(value);
                break;
              case '基准时长(s)':
                row.benchmark_duration = value === '' ? null : Number(value);
                break;
              case '输入Token总数':
                row.input_tokens = value === '' ? null : Number(value);
                break;
              case '生成Token总数':
                row.output_tokens = value === '' ? null : Number(value);
                break;
              case '请求吞吐量(req/s)':
                row.request_throughput = value === '' ? null : Number(value);
                break;
              case '输出Token吞吐量(tok/s)':
                row.output_token_throughput = value === '' ? null : Number(value);
                break;
              case '总Token吞吐量(tok/s)':
                row.total_token_throughput = value === '' ? null : Number(value);
                break;
              case '平均TTFT(ms)':
                row.avg_ttft = value === '' ? null : Number(value);
                break;
              case '中位TTFT(ms)':
                row.median_ttft = value === '' ? null : Number(value);
                break;
              case 'P99 TTFT(ms)':
                row.p99_ttft = value === '' ? null : Number(value);
                break;
              case '平均TPOT(ms)':
                row.avg_tpot = value === '' ? null : Number(value);
                break;
              case '中位TPOT(ms)':
                row.median_tpot = value === '' ? null : Number(value);
                break;
              case 'P99 TPOT(ms)':
                row.p99_tpot = value === '' ? null : Number(value);
                break;
            }
          });
          return row;
        });

        // 批量保存数据
        saveImportedData(rows);

      } catch (error) {
        message.error('CSV文件解析失败: ' + error.message);
      }
    };
    reader.readAsText(file);
    return false; // 阻止默认上传行为
  }, []);

  // 保存导入的数据
  const saveImportedData = async (rows) => {
    try {
      const response = await http.post('/api/model-storage/performance-test-data/batch/', {
        model_name: modelName,
        data: rows
      });

      if (response.success) {
        message.success(`成功导入 ${rows.length} 条数据`);
        fetchData(); // 重新获取数据
      } else {
        message.error('导入失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      message.error('导入失败: ' + error.message);
    }
  };

  // VTable 列配置 - 只读模式
  const getColumnsConfig = () => [
    {
      field: 'id',
      title: 'ID',
      width: 80,
      sort: true
    },
    {
      field: 'filename',
      title: '文件名',
      width: 150,
      sort: true
    },
    {
      field: 'success_requests',
      title: '成功请求数',
      width: 120,
      sort: true
    },
    {
      field: 'benchmark_duration',
      title: '基准时长(s)',
      width: 120,
      sort: true
    },
    {
      field: 'input_tokens',
      title: '输入Token总数',
      width: 130,
      sort: true
    },
    {
      field: 'output_tokens',
      title: '生成Token总数',
      width: 130,
      sort: true
    },
    {
      field: 'request_throughput',
      title: '请求吞吐量(req/s)',
      width: 150,
      sort: true
    },
    {
      field: 'output_token_throughput',
      title: '输出Token吞吐量(tok/s)',
      width: 180,
      sort: true
    },
    {
      field: 'total_token_throughput',
      title: '总Token吞吐量(tok/s)',
      width: 170,
      sort: true
    },
    {
      field: 'avg_ttft',
      title: '平均TTFT(ms)',
      width: 130,
      sort: true
    },
    {
      field: 'median_ttft',
      title: '中位TTFT(ms)',
      width: 130,
      sort: true
    },
    {
      field: 'p99_ttft',
      title: 'P99 TTFT(ms)',
      width: 130,
      sort: true
    },
    {
      field: 'avg_tpot',
      title: '平均TPOT(ms)',
      width: 130,
      sort: true
    },
    {
      field: 'median_tpot',
      title: '中位TPOT(ms)',
      width: 130,
      sort: true
    },
    {
      field: 'p99_tpot',
      title: 'P99 TPOT(ms)',
      width: 130,
      sort: true
    }
  ];

  // 初始化 VTable
  const initVTable = useCallback(async () => {
    if (!tableRef.current || !window.VTable) {
      return;
    }

    // 确保 process 对象存在
    if (typeof window.process === 'undefined') {
      window.process = {
        env: { NODE_ENV: 'development' },
        browser: true
      };
    }

    try {
      // 清理之前的实例
      if (tableInstanceRef.current) {
        try {
          tableInstanceRef.current.release();
        } catch (e) {
          // 忽略清理错误
        }
        tableInstanceRef.current = null;
      }

      // VTable 配置 - 只读模式，直接使用真实数据
      let tableData = data;

      const option = {
        records: tableData,
        columns: getColumnsConfig(),
        widthMode: 'standard',
        frozenColCount: 1,
        defaultRowHeight: 36,
        autoWrapText: true,
        limitMaxAutoWidth: 600,
        heightMode: 'autoHeight',
        keyboardOptions: {
          selectAllOnCtrlA: true,
          copySelected: true
        },
        menu: {
          contextMenuItems: [
            'copy',
            'selectAll'
          ]
        },
        theme: window.VTable.themes.DEFAULT.extends({
          defaultStyle: {
            textAlign: 'left',
            padding: [4, 8, 4, 8],
            fontSize: 14
          },
          headerStyle: {
            textAlign: 'center',
            fontWeight: 'bold',
            bgColor: '#f5f5f5'
          }
        })
      };

      // 创建表格实例
      const tableInstance = new window.VTable.ListTable(tableRef.current, option);
      tableInstanceRef.current = tableInstance;

      message.success('VTable 表格初始化成功！');

    } catch (error) {
      message.error(`表格初始化失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  }, [data]);

  // 初始化数据
  useEffect(() => {
    if (modelName) {
      fetchData();
    }
  }, [modelName, fetchData]);

  // 初始化表格
  useEffect(() => {
    if (window.VTable) {
      initVTable();
    }
  }, [data, initVTable]);

  return (
    <div style={{ padding: '20px' }}>
      <Card
        title={`${modelName} - 性能测试数据 (VTable版)`}
        style={{ position: 'relative' }}
      >
        {/* 工具栏 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchData}
              loading={loading}
            >
              刷新数据
            </Button>

            <Upload
              accept=".csv"
              showUploadList={false}
              beforeUpload={importCSV}
            >
              <Button icon={<ImportOutlined />}>
                导入CSV
              </Button>
            </Upload>

            <Button
              icon={<ExportOutlined />}
              onClick={exportData}
              disabled={data.length === 0}
            >
              导出CSV
            </Button>
          </Space>
        </div>

        {/* VTable 容器 */}
        <div
          ref={tableRef}
          style={{
            width: '100%',
            height: '600px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px'
          }}
        />

        {loading && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: 'rgba(255,255,255,0.8)',
            padding: '20px',
            borderRadius: '6px'
          }}>
            加载中...
          </div>
        )}
      </Card>
    </div>
  );
}

export default ModelDataTableVTable;
